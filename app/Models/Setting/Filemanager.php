<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 28.9.2016 г.
 * Time: 11:36 ч.
 */

namespace App\Models\Setting;

use App\Common\Media;
use App\Models\Base\AbstractFilemanager;
use App\Models\Queue\SiteQueue;
use App\Models\System\Storage as StorageSize;
use App\Traits\Crudling;
use Illuminate\Support\Facades\Storage;

class Filemanager extends AbstractFilemanager
{
    use Crudling;

    protected $appends = [
        'url', 'url_time', 'path'
    ];

    protected function casts(): array
    {
        return ['mtime' => 'datetime'];
    }

    #[\Override]
    protected static function boot()
    {
        parent::boot(); // TODO: Change the autogenerated stub

        static::deleted(function (Filemanager $file): void {
            if (Storage::exists($file_path = site_dir(config('upload.files_folder') . '/' . $file->dir . '/' . $file->name))) {
                if (app()->runningInConsole()) {
                    Storage::delete($file_path);
                } else {
                    SiteQueue::executeQueueTask('delete_file_manager_file', ['file' => $file_path, 'name' => $file->name, 'id' => $file->id]);
                }
            }

            \Illuminate\Support\Facades\DB::connection($file->getConnectionName())->reconnect();
            $file->storage()->delete();
        });

        static::saved(function (Filemanager $file): void {
            $file->storage()->delete();
            $file->storage()->save(new StorageSize([
                'size' => $file->size
            ]));
        });
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\MorphOne|StorageSize
     */
    public function storage()
    {
        return $this->morphOne(StorageSize::class, 'item');
    }

    /**
     * gets products by id
     *
     * @param string $where
     * @param \Pagging $pagging
     * @param string $order
     * @param string $order_direction
     *
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator|\Illuminate\Database\Eloquent\Collection|\Illuminate\Support\Collection
     *
     */
    public static function getList(
        $where = null,
        ?\Pagging $pagging = null,
        $order = 'id',
        $order_direction = 'desc',
        ?\Closure $callbackQuery = null,
        array $columns = ['*']
    ) {

        if ($order_direction !== 'asc') {
            $order_direction = 'desc';
        }

        if (!$order) {
            $order = 'id';
        }

        $model = static::orderBy($order, $order_direction);

        if ($where) {
            $model->whereRaw($where);
        }

        if ($pagging !== null) {
            $products = $model->paging($pagging);
        } else {
            $products = $model->get();
        }

        return $products;
    }

    /**
     *
     */
    public function generateUniqueName(): void
    {
        $unique_name = pathinfo($this->name, PATHINFO_FILENAME);
        $ext = pathinfo($this->name, PATHINFO_EXTENSION);
        while (static::where('name', $unique_name . '.' . $ext)->first()) {
            $unique_name .= '-' . uniqid();
        }

        $this->name = $unique_name . '.' . $ext;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getUrlAttribute($value)
    {
        return $this->exists ? Media::cdn_repository_file($this->name)[0] : null;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getUrlTimeAttribute($value)
    {
        return $this->exists ? Media::cdn_repository_file($this->name, $this->mtime->timestamp)[0] : null;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPathAttribute($value)
    {
        return $this->name ? Media::cdn_repository_file_path($this->name)[0] : null;
    }

}
