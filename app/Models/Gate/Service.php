<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: evgen
 * Date: 6/14/2018
 * Time: 1:12 PM
 */

namespace App\Models\Gate;

use App\Contracts\InvoiceItemContract;
use App\Helper\CloudcartCurrency;
use App\Helper\Format;
use App\Traits\Invoiceable;
use App\Traits\RecordCountryLimitations;
use Illuminate\Database\Eloquent\Builder;
use Spatie\Translatable\HasTranslations;

/**
 * @property integer $price
 * @property string $currency
 * @property string $name
 * @property integer $billing_cycle
 * @property string $billing_period
 * @property null|string $billing_period_text
 * @property string $name_translated
 * @property int $price_without_vat
 * @property int $user_price_without_vat
 * @property int $user_price
 * @property string $user_price_formatted
 * @property float $price_input
 * @property string $description
 * @property int $archived
 * @property ServiceTag $tag
 * @method static self|Builder ecosystem
 * @method static self|Builder inhouse
 * @method static self|Builder active
 * @method static self|Builder archived
 * @method static self|Builder public
 * @method static self|Builder recommended
 */
class Service extends BaseModel implements InvoiceItemContract
{
    use HasTranslations;
    use Invoiceable;
    use RecordCountryLimitations;

    public $translatable = [
        'name',
        'description',
    ];

    /**
     * @var string
     */
    protected $connection = 'gate';

    /**
     * @var string
     */
    protected $table = 'cc_gate.services';

    //    protected $table = 'cc_gate_prod.services';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'name_translated',
        'description_translated',
        'price_input',
        'price_formatted',
        'billing_period',
        'model_type',
        'price_without_vat_input',
    ];

    protected $_currency;

    /**
     * Create a new Eloquent model instance.
     *
     * @param array $attributes
     */
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        //        $this->_currency = Currency::get(config('billing.currency.default'));
    }

    /**
     * @param Builder $query
     *
     * @return mixed
     */
    public function scopeActive($query)
    {
        return $query->where('archived', 0);
    }

    /**
     * @param Builder $query
     *
     * @return Builder
     */
    public function scopeEcosystem($query)
    {
        return $query->where('ecosystem', 1);
    }

    /**
     * @param Builder $query
     *
     * @return Builder
     */
    public function scopeInhouse($query)
    {
        return $query->where('ecosystem', 0);
    }

    /**
     * @param Builder $query
     *
     * @return mixed
     */
    public function scopeArchived($query)
    {
        return $query->where('archived', 1);
    }

    /**
     * @param Builder $query
     *
     * @return mixed
     */
    public function scopePublic($query)
    {
        return $query->where('public', 1)->where('archived', 0);
    }

    /**
     * @param Builder $query
     *
     * @return mixed
     */
    public function scopeRecommended($query)
    {
        return $query->where('recommend', 1)->where('archived', 0);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function group()
    {
        return $this->belongsTo(ServiceGroup::class, 'group_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function tag()
    {
        return $this->belongsTo(ServiceTag::class, 'tag_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function expertServices()
    {
        return $this->hasMany(ExpertService::class, 'service_id')
            ->where('active', 1);
    }

    /**
     * Encode the given value as JSON.
     *
     * @param mixed $value
     * @return string
     */
    protected function asJson($value, $flags = 0): string
    {
        return json_encode($value, $flags ? : JSON_UNESCAPED_UNICODE);
    }

    /**
     * @inheritDoc
     */
    public function invoiceItemDescription($params = [])
    {
        return $this->name_translated;
    }

    /**
     * @inheritDoc
     */
    public function getGoogleTagManagerData(): array
    {
        return [
            'name' => $this->getTranslation('name', 'en'),
            'id' => $this->getKey(),
            'variant' => $this->billing_period,
            'price' => $this->price_input,
            'quantity' => 1,
            'brand' => 'CloudCart',
            'category' => 'Services',
        ];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getNameTranslatedAttribute($value): mixed
    {
        return $this->translate('name', app()->getLocale());
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getDescriptionTranslatedAttribute($value): mixed
    {
        return $this->translate('description', app()->getLocale());
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getDescriptionHtmlAttribute($value): string
    {
        return nl2br($this->description);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPriceInputAttribute($value): string|int
    {
        if (!$currency = $this->currency) {
            $currency = config('billing.currency.default');
        }
        return Format::moneyInput($this->price, $currency);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPriceFormattedAttribute($value): string
    {
        return Format::money($this->price, true, $this->currency);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPriceWithoutVatAttribute($value)
    {
        return $this->price;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPriceWithoutVatInputAttribute($value): string|int
    {
        return Format::moneyInput($this->price_without_vat);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPriceVatInputAttribute($value): string|int
    {
        $currency = $this->currency;
        $vat = $this->price - $this->price_without_vat;
        return Format::moneyInput($vat, $currency);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getUserPriceAttribute($value)
    {
        $userCurrency = site()->user->currency;
        $price = $this->price;
        if ($this->currency != $userCurrency) {
            $price = CloudcartCurrency::convert($price, $userCurrency, $this->currency);
        }
        return $price;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getUserPriceFormattedAttribute($value): string
    {
        $userCurrency = site()->user->currency;
        return Format::money($this->user_price, true, $userCurrency) . ' / ' . __('global.' . $this->billing_period);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getUserPriceWithoutVatAttribute($value)
    {
        $userCurrency = site()->user->currency;
        $price = $this->price_without_vat;
        if ($this->currency != $userCurrency) {
            $price = CloudcartCurrency::convert($price, $userCurrency, $this->currency);
        }
        return $price;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getUserPriceWithoutVatInputAttribute($value): string|int
    {
        $userCurrency = site()->user->currency;
        $price = $this->user_price_without_vat;
        return Format::moneyInput($price, $userCurrency);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getUserPriceWithoutVatFormattedAttribute($value): string
    {
        return Format::money($this->user_price_without_vat, true, $this->currency) . ' / ' . __('global.' . $this->billing_period);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getBillingPeriodTextAttribute($value)
    {
        switch ($this->billing_cycle) {
            case null:
                return __('plan.period.once');
            case 1:
                return __('plan.period.month');
            case 12:
                return __('plan.period.year');
            case 24:
                return __('plan.period.2years');
            default:
                return trans_choice('plan.period.period', $this->billing_cycle, ['period' => $this->billing_cycle]);
        }
    }
}
