<?php

declare(strict_types=1);

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateMagentoCustomersAddressesTable extends Migration
{
    protected $table = '@app_magento_customers_addresses';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create($this->table, function (Blueprint $table): void {
            $table->bigIncrements('id');
            $table->integer('customer_address_id', false, true)->index()->nullable()->default(null);
            $table->integer('customer_id', false, true)->index()->nullable()->default(null);
            $table->timestamp('created_at')->nullable()->default(null);
            $table->timestamp('updated_at')->nullable()->default(null);
            $table->boolean('get_info')->index()->default(0)->nullable();
            $table->string('city')->nullable()->default(null);
            $table->string('company')->nullable()->default(null);
            $table->string('country_id', 3)->nullable()->default(null);
            $table->string('firstname')->nullable()->default(null);
            $table->string('lastname')->nullable()->default(null);
            $table->string('postcode')->nullable()->default(null);
            $table->string('region')->nullable()->default(null);
            $table->string('street', 1024)->nullable()->default(null);
            $table->string('telephone')->nullable()->default(null);
            $table->string('fax')->nullable()->default(null);
            $table->string('vat_id')->nullable()->default(null);
            $table->integer('vat_is_valid', false, true)->index()->nullable()->default(null);
            $table->boolean('is_default_billing')->index()->nullable()->default(null);
            $table->boolean('is_default_shipping')->index()->nullable()->default(null);
            $table->foreign('customer_id')->on('@app_magento_customers')
                ->references('customer_id')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists($this->table);
    }
}
