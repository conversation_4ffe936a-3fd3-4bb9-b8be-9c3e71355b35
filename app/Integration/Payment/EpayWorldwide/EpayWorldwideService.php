<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 13.4.2017 г.
 * Time: 14:18
 */

namespace App\Integration\Payment\EpayWorldwide;

use App\Exceptions\Error;
use App\Exceptions\PaymentBadRequest;
use App\Integration\Payment\AbstractService;
use App\Models\Gateway\Currency;
use App\Models\Gateway\PaymentLogs;
use App\Models\Gateway\PaymentProviderConfiguration;
use App\Models\Gateway\Payments;
use App\Models\Router\Exceptions;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Omnipay\Common\Message\NotificationInterface;
use Omnipay\Paycenter\Message\RedirectResponse;
use Omnipay\Paycenter\RedirectGateway;
use Omnipay\Common\GatewayFactory;

class EpayWorldwideService extends AbstractService
{
    /**
     * An array with supported currencies.
     * @var array
     */
    public static $supportedCurrencies = [
        '008' => 'ALBANIAN LEK (ALL)',
        '032' => 'ARGENTINA PESO (ARS)',
        '036' => 'AUSTRALIAN DOLLAR (AUD)',
        124 => 'CANADIAN DOLLAR (CAD)',
        152 => 'CHILEAN PESO (CLP)',
        156 => 'CHINESE YUAN (CNY)',
        170 => 'COLOMBIAN PESO (COP)',
        191 => 'CROATIAN KUNA (HRK)',
        203 => 'CZECH KORUNA (CZK)',
        208 => 'DANISH KRONE (DKK)',
        344 => 'HONG KONG DOLLAR (HKD)',
        348 => 'FIORINT (HUF)',
        356 => 'INDIAN RUPEE (INR)',
        360 => 'RUPIAH (IDR)',
        376 => 'ISRAELI NEW SHEQEL (ILS)',
        392 => 'YEN (JPY)',
        398 => 'TENGE (KZT)',
        410 => 'WON (KRW)',
        414 => 'KUWAITI DINAR (KWD)',
        440 => 'LITHUANIAN LITAS (LTL)',
        446 => 'PATACA (MOP)',
        458 => 'MALAYSIAN RINGGIT (MYR)',
        484 => 'MEXICAN PESO (MXN)',
        504 => 'MORROCAN DIRHAM (MAD)',
        554 => 'NEW ZEALAND DOLLAR (NZD)',
        578 => 'NORWEGIAN KRONE (NOK)',
        604 => 'NUEVO SOL (PEN)',
        608 => 'PHILIPPINE PESO (PHP)',
        643 => 'RUSSIAN ROUBLE (RUB)',
        682 => 'SAUDI RIYAL (SAR)',
        702 => 'SINGAPORE DOLLAR (SGD)',
        710 => 'RAND (ZAR)',
        752 => 'SWEDISH KRONA (SEK)',
        756 => 'SWISS FRANC (CHF)',
        764 => 'BAHT (THB)',
        784 => 'UNITED ARAB EMIRATES DIRHAM (AED)',
        818 => 'EGYPTIAN POUND (EGP)',
        826 => 'POUND STERLING (GBP)',
        840 => 'US DOLLAR (USD)',
        933 => 'BELARUSIAN RUBLE (BYN)',
        937 => 'BOLIVAR FUERTE (VEF)',
        941 => 'SERBIAN DINAR (RSD)',
        946 => 'ROMANIAN LEU (RON)',
        949 => 'TURKISH LIRA (TRY)',
        975 => 'BULGARIAN LEV (BGN)',
        978 => 'EURO (EUR)',
        980 => 'UKRAINIAN HRYVNIA (UAH)',
        985 => 'POLISH ZLOTY (PLN)',
        986 => 'BRAZILIAN REAL (BRL)',
    ];

    protected $currency;

    /**
     * @var RedirectGateway
     */
    protected $omnipay;

    /**
     * @param Request $request
     * @return Payments
     * @throws Exception
     */
    public static function getPaymentFromRequest(Request $request)
    {
        if ($request->isMethod('post')) {
            parse_str((string) $request->input('Parameters'), $params);
            $id = $params['pid'] ?? null;
        } else {
            $id = $request->get('pid');
        }

        if (empty($id)) {
            throw new PaymentBadRequest('Bad Request', $request);
        }

        /** @var Payments $payment */
        $payment = Payments::find($id);
        if (empty($payment)) {
            throw new PaymentBadRequest('Bad Request', $request);
        }

        return $payment;
    }

    protected function loadConfig()
    {
        $factory = new GatewayFactory();
        $this->omnipay = $factory->create('Paycenter_Redirect');

        $provider = PaymentProviderConfiguration::findByProvider('epay_worldwide');

        if (!empty($provider->configuration)) {
            $this->config = $provider->configuration;
            $this->omnipay->setTestMode(empty($provider->configuration['mode']));
        }

        $this->currency = $provider->configuration['currency'] ?? 978;
    }

    /**
     * @param Payments $payment
     * @return array
     * @throws Exception
     */
    public function purchase(Payments $payment): array
    {
        $this->loadConfig();

        if ($this->getCurrencyISO() != $payment->currency) {
            $payment->amount = round(Currency::convert(
                $payment->amount,
                $payment->currency,
                $this->getCurrencyISO()
            ));
            $payment->currency = $this->getCurrencyISO();
        }

        $order_id = (setting('order_id_display') == 'increment_hash') ?
            $payment->orderPayment->order->increment_hash :
            $payment->orderPayment->order_id;

        try {
            $requestData = [
                'Username' => $this->config['Username'],
                'Password' => hash('md5', (string) $this->config['Password']),
                'MerchantId' => $this->config['MerchantID'],
                'PosId' => $this->config['PosID'],
                'AcquirerId' => $this->config['AcquirerID'],
                'MerchantReference' => $order_id,
                'RequestType' => '02',
                'ExpirePreauth' => 0,
                'Amount' => $payment->amount / 100,
                'CurrencyCode' => $this->currency,
                'Installments' => 0,
                'Bnpl' => 0,
                'Parameters' => 'pid=' . $payment->getKey(),
            ];

            $request = $this->omnipay->purchase($requestData);
            /** @var RedirectResponse $response */
            $response = $request->send();

            PaymentLogs::siteToProvider($payment, $request->getData(), $response->getRedirectData());

            $payment->provider_reference_id = $response->getTransactionReference();
            $payment->provider_data = $response->getData();
            $payment->sync();
        } catch (Exception $exception) {
            Exceptions::createFromThrowable($exception);
            throw new Error($exception->getMessage());
        }

        $redirectHtml = $response->getRedirectResponse()->getContent();

        return [
            'payment' => [
                'payment_id' => $payment->id,
            ],
            'status' => Payments::STATUS_REQUESTED,
            'htmlRedirectForm' => $redirectHtml,
        ];
    }

    /**
     * @param Payments $payment
     * @param array $data
     * @throws Exception
     */
    public function completePurchase(Payments $payment, array $data = []): void
    {
        // canceled by the user
        if ($data['pid'] ?? null && $data['pid'] == $payment->getKey()) {
            if ($payment->status == Payments::STATUS_INITIATED) {
                PaymentLogs::providerToSite($payment, $data, [], 'return');
                $payment->status = Payments::STATUS_CANCELED;
                $payment->provider_data = array_merge((array)$payment->provider_data, [
                    'status' => 'Canceled by the user'
                ]);
                $payment->sync();
            }

            return;
        }

        $this->loadConfig();

        try {
            $data = Arr::only((array)$payment->provider_data, [
                'TranTicket',
                'PosId',
                'AcquirerId',
            ]);

            $request = $this->omnipay->completePurchase($data);
            $response = $request->send();

            PaymentLogs::providerToSite($payment, $request->getData(), $response->getData(), 'return');
        } catch (Exception $exception) {
            throw $exception;
        }

        $paymentStatus = $payment->status;
        if ($paymentStatus != $this->getTransactionState($response->getTransactionStatus())) {
            $payment->provider_reference_id = $response->getTransactionReference();
            $payment->status = $this->getTransactionState($response->getTransactionStatus());
            $payment->provider_data = $response->getData();
            $payment->sync();
        }
    }

    /**
     * @param $status
     * @return string
     */
    protected function getTransactionState($status): string
    {
        return match ($status) {
            NotificationInterface::STATUS_COMPLETED => Payments::STATUS_COMPLETED,
            NotificationInterface::STATUS_PENDING => Payments::STATUS_PENDING,
            default => Payments::STATUS_FAILED,
        };
    }

    protected function getCurrencyISO(): string
    {
        $str = self::$supportedCurrencies[$this->currency];

        preg_match('/\((.*?)\)/s', (string) $str, $m);

        return $m[1] ?? 'EUR';
    }
}
