<?php

declare(strict_types=1);

$stre = '
Àà | Aa

Èè | Ee

Ìì | Ii

Ǹǹ | Nn

Òò | Oo

Ùù | Uu

Ẁẁ | Ww

Ỳỳ |  Yy

Ââ | Aa

Ĉĉ | Cc

Êê | Ee

Ĝĝ | Gg

Ĥĥ | Hh

Îî | Ii

Ĵĵ | Jj

Ôô | Oo

Ŝŝ | Ss

Ûû | Uu

Ŵŵ | Ww

Ŷŷ | Yy

Ẑẑ | Zz

Ⱥⱥ | Aa

Ƀƀ | Bb

Ȼȼ | Cc

Đđ | Dd

Ɇɇ | Ee

Ǥǥ | Gg

Ꞡꞡ | Gg

Ħħ | Hh

Ɨɨ | Ii

Ɉɉ | Jj

Ꝁꝁ | Kk

Ꞣꞣ | Kk

Łł | Ll

Ꞥꞥ | Nn

Øø | Oo

Ᵽᵽ | Pp

Ꝗꝗ | Qq

Ɍɍ | Rr

Ꞧꞧ | Rr

Ꞩꞩ | Ss

Ŧŧ | Tt

Ʉʉ | Uu

Ɏɏ | Yy

Ƶƶ | Zz

Ꝥꝥ | Pp

Őő | Oo

Űű | Uu

Ãã | Aa

Ẽẽ | Rr

Ĩĩ | Ii

Ññ | Nn

Õõ | Oo

Ũũ | Uu

Ṽṽ | Vv

Ỹỹ | Yy

Åå | Aa

Ůů | Uu

W̊ẘ | Ww

Y̊ẙ | Yy

D̦d̦ | Dd

Ḑ | D

Șș | Ss

Şş | Ss

Țț | Tt

Ţţ | Tt

Ăă | Aa

Îî | Ii

Ââ | Aa
';

dd(\Illuminate\Support\Str::ascii($stre), \App\Integration\Payment\Btepos\F::removeAccents($stre));
//$btepos = [
//    'city' => 'București',
//];
//$u = json_encode($btepos, JSON_UNESCAPED_UNICODE);
//dnd($u, mb_detect_encoding($u));
//$a = json_encode($btepos);
//dd($a, mb_detect_encoding($a));
