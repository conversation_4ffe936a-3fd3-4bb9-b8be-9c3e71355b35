{if isset($checkoutId) && isset($requestUrl)}
    <script src="{$requestUrl}/v1/paymentWidgets.js?checkoutId={$checkoutId}"></script>
{/if}

<div class="_modal modal fade" id="catalyst_pay-popup" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-small">
        <div class="modal-content" style="background-color: transparent !important; box-shadow: none !important; border: none !important;">
            <div class="modal-body">
                <form action="{$returnUrl}" class="paymentWidgets" data-brands="{$brands}"></form>
            </div>
        </div>
    </div>
</div>

<script>
    var popup = $('#catalyst_pay-popup');
    if (popup.length) {
        popup.remove();
    }

    // $('body').append(popup);

    CCHelper.removeLoader('body');
    popup.modal();
</script>