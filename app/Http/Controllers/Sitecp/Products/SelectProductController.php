<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 30.7.2018 г.
 * Time: 09:32 ч.
 */

namespace App\Http\Controllers\Sitecp\Products;

use App\Exceptions\Error;
use App\Exceptions\Fault;
use App\Helper\FilterSearch\Product\FilterSelectProduct;
use App\Helper\Format;
use App\Helper\Grid;
use App\Helper\Import\ProductsFormatter;
use App\Helper\SiteCp\Tools;
use App\Http\Request\Sitecp\Products\SelectProductRequest;
use App\Models\Product\Variant;
use App\Models\Product\Vendor;
use Illuminate\Http\Response;
use App\Models\Product\Product;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use App\Helper\SiteCp\Search;
use LinkerCp;

class SelectProductController extends Controller
{
    /**
     * @var integer
     */
    protected $records_exist;

    /**
     * @var Search
     */
    protected $search_object;

    /**
     * @param Request $request
     * @return Response
     * @throws Error
     * @throws Fault
     */
    public function index(Request $request)
    {
        $this->records_exist = Product::count();
        $this->search_object = new Search(FilterSelectProduct::class);

        return $request->isMethod('post') ? $this->_grid($request) : $this->_init($request);
    }

    /**
     * @param Request $request
     * @param $product_id
     * @return Response
     * @throws Error
     */
    public function select(Request $request, $product_id)
    {
        $product = Product::getDiscounts([
            'customer_group_id' => null,
        ])->find($product_id);
        if (!$product) {
            throw new Error(__('global.err.no_longer_exists'));
        }

        $product = $product->format();

        $variants = $product->variants->map->formatDiscountsVariants($product);

        $parameters = [];
        $variants = $variants->map(function (Variant $variant) use ($product, &$parameters): \App\Models\Product\Variant {
            $variant = $variant->format();
            if (!is_null($variant->getAttribute('price_discounted'))) {
                $variant->setAttribute('price_discounted_formatted', Format::money($variant->getAttribute('price_discounted')));
                $variant->setAttribute('price_discounted_input', Format::moneyInput($variant->getAttribute('price_discounted')));

                $variant->setAttribute('price_saved', $variant->price - $variant->getAttribute('price_discounted'));
                $variant->setAttribute('price_saved_formatted', Format::money($variant->getAttribute('price_saved')));
                $variant->setAttribute('price_saved_input', Format::moneyInput($variant->getAttribute('price_saved')));

                $variant->setAttribute('old_price_formatted', $variant->price_formatted);
                $variant->price_formatted = $variant->getAttribute('price_discounted_formatted');
            }

            for ($i = 1; $i <= $product->total_variants; $i++) {
                $parameters[$product->getAttribute('p' . $i)][$variant->getAttribute('v' . $i)] = $variant->getAttribute('v' . $i);
            }

            return $variant;
        })->keyBy('id');

        return \Illuminate\Support\Facades\View::modal('products.select-product.select', [
            'product_id' => $product->id,
            'product' => $product,
            'variants' => $variants,
            'variants_json' => json_encode($variants),
            'parameters' => array_map('array_values', $parameters),
            'allow_change_quantity' => $request->input('options.change-quantity', 1),
            'allow_change_price' => $request->input('options.change-price', 1),
            'opener_selector' => $request->input('options.opener'),
        ], __('order.header.add_product'));
    }

    /**
     * @param SelectProductRequest $request
     * @param $product_id
     * @return Response
     * @throws Error
     */
    public function validateSelect(SelectProductRequest $request, $product_id)
    {
        $product = Product::getDiscounts([
            'customer_group_id' => null,
        ])->find($product_id);

        if (!$product) {
            throw new Error(__('global.err.no_longer_exists'));
        }

        return response([
            'status' => 'success',
            'msg' => null
        ]);
    }

    /**
     * @param Request $request
     * @return Response
     */
    protected function _init(Request $request)
    {
        return \Illuminate\Support\Facades\View::modal('products.select-product.list', [
            'records_exist' => $this->records_exist,
            'search_object' => $this->search_object,
            'options' => $request->input('options', [])
        ], __('order.header.add_product'));
    }

    /**
     * @param Request $request
     * @return Response
     * @throws Error
     */
    protected function _grid(Request $request)
    {
        $filters = $request->input('filters');
        if (empty($filters)) {
            $filters = [];
        }

        $grid = new Grid();

        [$where, $joins, $filter_strings] = $this->search_object->search($filters);

        $products = Product::selectRaw('*, products.*')->quantity()->with(['vendor', 'image'])->getRelationList($where, $joins, $grid);

        $products_variants = null;
        if ($products->count()) {
            $products_variants = Variant::getByProductIds($products->keys()->all());
        }

        $formatted = ProductsFormatter::formatParametersForList($products, $products_variants);

        foreach ($products as $product) {

            $product_name = $product->name;

            $product->image_grid = '<span class="image" style="background-image: url(' . $product->getImage('300x300') . ')"></span>';
            $product->name = '<div class="pull-left">' .
                '<span class="editable">
                    <a href="' . LinkerCp::product($product->id) . '" target="_blank" class="product-image">
                        <span class="image tooltips" title="' . __('global.action.view_details') . '" data-html="true" data-placement="top" style="background-image: url(' . $product->getImage('150x150') . ')"><span class="overlay"><i class="fal fa-store fa-lg"></i></span></span>
                    </a>
                    <div class="name-holder">' . $product->name;

            if (isset($product->vendor_id) && ($product->vendor_id != null)) {
                $vendor = !$product->vendor ? Vendor::find($product->vendor_id) : $product->vendor;
                $product->name .= '<span class="product-vendor">' . __('product.by') . ' <a href="' . route('admin.select.product.list', ['filters' => ['productVendor' => $product->vendor_id], 'options' => ['change-quantity' => $request->input('options.change-quantity', 1), 'change-price' => $request->input('options.change-price', 1), 'opener' => $request->input('opener')]]) . '" target="_blank" title="' . ($vendor ? $vendor->name : null) . '">' . ($vendor ? $vendor->name : null) . '</a></span>';
            }

            $product->name .= '</div></span>';

            $product->parameters_formatted = __('order.info.product_add.no_variants');

            if (!empty($product->p1)) {

                $product->parameters_formatted = Tools::dataHolder(
                    'x fal fa-sliders-v',
                    __('product.label.parameters'),
                    false,
                    false,
                    '_self',
                    [
                        'data' => 'data-cc-toggle="class" data-cc-toggle-class="active" data-cc-toggle-target=".p-' . $product->id . '"',
                        'class' => 'count-holder action'
                    ]
                );

                $parameter_items_formatted = '';
                foreach ($formatted[$product->id] as $parameter => $values) {
                    $parameter_items_formatted .= '<li class="parameter-holder">' .
                        '<span class="parameter-name">' . $parameter . '</span>' .
                        '<span class="parameter-values">' . implode(', ', $values) . '</span>' .
                        '</li>';

                }

                $product->parameters_formatted .= '<ul class="parameters-drop-list p-' . $product->id . '"><div class="scroll-box perfect-scroll">';
                $product->parameters_formatted .= $parameter_items_formatted;
                $product->parameters_formatted .= '</div></ul>';
            }

            $product->edit = '<a href="#" data-modal-size="small" class="add-product-in-order" data-modal-ajax="' . route('admin.select.product.add', [$product->id, 'options' => ['change-quantity' => $request->input('options.change-quantity', 1), 'change-price' => $request->input('options.change-price', 1), 'opener' => $request->input('options.opener')]]) . '" title="' . $product_name . '"><i class="fal fa-plus-circle cc-purple"></i>' . __('order.product.add_product') . '</a>';

        }

        return $grid->generateIlluminate($products, $filter_strings);
    }

}
