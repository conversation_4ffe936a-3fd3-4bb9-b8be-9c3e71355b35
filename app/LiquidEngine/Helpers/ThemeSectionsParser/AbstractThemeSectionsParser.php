<?php

declare(strict_types=1);

namespace App\LiquidEngine\Helpers\ThemeSectionsParser;

use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Contracts\View\View;
use Parsedown;

abstract class AbstractThemeSectionsParser
{
    protected array $settings;

    protected \Illuminate\Support\Collection $config;

    /**
     * @var bool $block
     */
    protected $block = false;

    /**
     * AbstractThemeSectionsParser constructor.
     * @param array $settings
     * @param Collection $config
     */
    public function __construct(array $settings, Collection $config)
    {
        $this->settings = $settings;
        $this->config = $config;
    }

    /**
     * @param bool $block
     * @return $this
     */
    public function setBlock(bool $block) : self
    {
        $this->block = $block;
        return $this;
    }

    /**
     * @return bool
     */
    public function isBlock() : bool
    {
        return $this->block;
    }

    /**
     * @param array $data
     * @return array
     */
    public function update(array $data) : array
    {
        return [];
    }

    /**
     * @return string|null
     */
    public function getId() : ?string
    {
        if(empty($this->settings['id']) || !is_scalar($this->settings['id'])) {
            return null;
        }

        return $this->settings['id'];
    }

    /**
     * @return string|null
     */
    public function getInfo() : ?string
    {
        if(!empty($info = $this->_getLocaleLabel($this->settings, 'info'))) {
            $parseDown = new Parsedown();
            return $parseDown->line($info);
        }

        return null;
    }

    /**
     * @return View|null
     */
    abstract public function render() : ?View;

    /**
     * @return string|null
     */
    protected function getContent() : ?string
    {
        return $this->_getLocaleLabel($this->settings, 'content');
    }

    /**
     * @return string|null
     */
    protected function getLabel() : ?string
    {
        return $this->_getLocaleLabel($this->settings, 'label');
    }

    /**
     * @return array|null
     */
    protected function getOptions() : ?array
    {
        if(empty($this->settings['options'])) {
            return null;
        }

        $options = $this->_normaliseRangeForOptions($this->settings['options']);
        if(empty($options)) {
            return [];
        }

        return array_values(array_filter(array_map(function(array $option) {
            if(!array_key_exists('value', $option)) {
                return null;
            }

            $option['label'] = $this->_getLocaleLabel($option, 'label') ? : $option['value'];
            return $option;
        }, $options)));
    }

    /**
     * @param $range
     * @return array
     */
    private function _normaliseRangeForOptions($range): array
    {
        if(is_array($range)) {
            return $range;
        }

        if(preg_match('/^(\d*)-(\d*)(\:(\d*))?$/', $range, $match) && $match[1] < $match[2]) {
            if(!empty($match[4]) && $match[4] > 1) {
                $range = range($match[1], $match[2], $match[4]);
            } else {
                $range = range($match[1], $match[2]);
            }

            return array_map(function($value): array {
                return [
                    'value' => $value,
                    'label' => $value
                ];
            }, $range);
        }

        return [];
    }

    /**
     * @param $data
     * @param $key
     * @return string|null
     */
    private function _getLocaleLabel($data, string $key) : ?string
    {
        if(empty($data) || empty($data[$key])) {
            return null;
        }

        $info = $data[$key];
        if(is_scalar($info)) {
            // Check for t: prefix for translation keys
            if(is_string($info) && str_starts_with($info, 't:')) {
                $translationKey = substr($info, 2); // Remove 't:' prefix

                // Try to get translation from schema files first
                $translation = \App\LiquidEngine\Helpers\ThemeSectionsParser::getSchemaTranslation($translationKey);

                // If found in schema files, return it
                if($translation !== null) {
                    return $translation;
                }

                // Fallback to Laravel's translation system
                $translation = __($translationKey);

                // If translation exists and is different from the key, return it
                if($translation !== $translationKey) {
                    return $translation;
                }

                // Fallback to English if current locale translation doesn't exist
                $fallbackLocale = config('app.fallback_locale', 'en');
                if(app()->getLocale() !== $fallbackLocale) {
                    $fallbackTranslation = trans($translationKey, [], $fallbackLocale);
                    if($fallbackTranslation !== $translationKey) {
                        return $fallbackTranslation;
                    }
                }

                // If no translation found, return the original string without t: prefix
                return $translationKey;
            }

            return $info;
        }

        if(!is_array($info)) {
            return null;
        }

        $fallBack = config('app.fallback_locale');
        if(array_key_exists(app()->getLocale(), $info) && !empty($info[app()->getLocale()])) {
            if(!is_scalar($info[app()->getLocale()])) {
                return null;
            }

            return $info[app()->getLocale()];
        } elseif(array_key_exists($fallBack, $info) && !empty($info[$fallBack])) {
            if(is_scalar($info[$fallBack])) {
                return $info[$fallBack];
            }
        }

        return Arr::first(array_filter($info));
    }

}
