<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Category;

use App\Exceptions\Error;
use App\LiquidEngine\LiquidHelpers\Format\LiquidImageAttribute;
use App\Models\Category\PropertyOption as CategoryPropertyOptionModel;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractAbstractDrop;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;
use App\LiquidEngine\Traits\DropImageToArray;

/**
 * PropertyOption Drop Class
 *
 * This class represents a property option in Liquid templates, providing access to option properties
 * and methods for Shopify compatibility, accessibility, and SEO features.
 *
 * Properties:
 * - id: The unique identifier of the option
 * - value: The option's value
 * - description: The option's description
 * - url_handle: The URL-friendly handle for the option
 * - url: The full URL to the option's page
 * - property: The associated property object
 * - has_image: Whether the option has an image
 * - image: The option's image data
 * - metafields: Custom metadata fields
 * - sort_order: The option's sort order
 * - usage_count: Number of products using this option
 * - is_active: Whether the option is active
 * - is_default: Whether this is the default option
 *
 * @package App\LiquidEngine\LiquidHelpers\Drop\Category
 */
class PropertyOption extends AbstractAbstractDrop
{
    use DropImageToArray;

    /**
     * @var CategoryPropertyOptionModel The underlying property option model
     */
    protected CategoryPropertyOptionModel $_option;

    /**
     * @var Category|null The associated category, if any
     */
    protected ?Category $_category;

    /**
     * PropertyOption constructor.
     *
     * @param CategoryPropertyOptionModel $option The property option model
     * @param Category|null $category The associated category
     */
    public function __construct(CategoryPropertyOptionModel $option, ?Category $category = null)
    {
        $this->_option = $option;
        $this->_category = $category;
    }

    /**
     * Get the option's ID
     *
     * @return int The option ID
     */
    public function id(): int
    {
        return $this->_option->id;
    }

    /**
     * Get the option's value
     *
     * @return string The option value
     */
    public function value(): string
    {
        return $this->_option->value;
    }

    /**
     * Get the option's description
     *
     * @return string|null The option description
     */
    public function description(): ?string
    {
        return $this->_option->description;
    }

    /**
     * Get the option's URL handle
     *
     * @return string The URL handle
     */
    public function url_handle(): string
    {
        return $this->_option->url_handle;
    }

    /**
     * Get the option's URL
     *
     * @return string The full URL to the option's page
     */
    public function url(): string
    {
        if ($this->_category && $this->property()) {
            return route('category.view', [
                'slug' => $this->_category->url_handle(),
                'property' => [$this->property()->url_handle() => $this->url_handle()]
            ]);
        }
        return '#';
    }

    /**
     * Get the associated property
     *
     * @return Property|null The property object
     */
    public function property(): ?Property
    {
        if ($this->_option->relationLoaded('property')) {
            $clone = clone $this->_option->property;
            return new Property($clone->clearRelations());
        }
        return null;
    }

    /**
     * Check if the option has an image
     *
     * @return bool Whether the option has an image
     */
    public function has_image(): bool
    {
        return $this->_option->hasImage();
    }

    /**
     * Get the option's image
     *
     * @return LiquidImageAttribute The image data
     */
    public function image(): LiquidImageAttribute
    {
        $formatter = new UrlImageFormat($this->_option);
        return $formatter->getLiquidImage();
    }

    /**
     * Shopify compatibility: handle
     * Returns the option handle (url_handle)
     *
     * @return string The option handle
     */
    public function handle(): string
    {
        return $this->url_handle();
    }

    /**
     * Shopify compatibility: title
     * Returns the option title (value)
     *
     * @return string The option title
     */
    public function title(): string
    {
        return $this->value();
    }

    /**
     * Shopify compatibility: metafields
     * Returns the option metafields as an array
     *
     * @return array<string, mixed> The metafields data
     */
    public function metafields(): array
    {
        return $this->_option->metafields ?? [];
    }

    /**
     * Shopify compatibility: published_at
     * Returns the published date as a Date Drop
     *
     * @return \App\LiquidEngine\LiquidHelpers\Drop\Date|null The published date
     */
    public function published_at(): ?\App\LiquidEngine\LiquidHelpers\Drop\Date
    {
        return $this->_option->created_at ? new \App\LiquidEngine\LiquidHelpers\Drop\Date($this->_option->created_at) : null;
    }

    /**
     * Shopify compatibility: updated_at
     * Returns the updated date as a Date Drop
     *
     * @return \App\LiquidEngine\LiquidHelpers\Drop\Date|null The updated date
     */
    public function updated_at(): ?\App\LiquidEngine\LiquidHelpers\Drop\Date
    {
        return $this->_option->updated_at ? new \App\LiquidEngine\LiquidHelpers\Drop\Date($this->_option->updated_at) : null;
    }

    /**
     * Shopify compatibility: sort_order
     * Returns the sort order of the option
     *
     * @return int|null The sort order
     */
    public function sort_order(): ?int
    {
        return $this->_option->sort ?? null;
    }

    /**
     * SEO: meta_title
     * Returns the SEO meta title for the option
     *
     * @return string|null The meta title
     */
    public function meta_title(): ?string
    {
        return $this->_option->seo_title ?? $this->value();
    }

    /**
     * SEO: meta_description
     * Returns the SEO meta description for the option
     *
     * @return string|null The meta description
     */
    public function meta_description(): ?string
    {
        return $this->_option->seo_description ?? $this->description();
    }

    /**
     * SEO: canonical_url
     * Returns the canonical URL for the option
     *
     * @return string|null The canonical URL
     */
    public function canonical_url(): ?string
    {
        return $this->_option->canonical_url ?? $this->url();
    }

    /**
     * SEO: robots
     * Returns the robots meta tag content
     *
     * @return string The robots meta content
     */
    public function robots(): string
    {
        return $this->_option->robots ?? 'index, follow';
    }

    /**
     * SEO: structured_data
     * Returns the JSON-LD structured data for the option
     *
     * @return array<string, mixed> The structured data
     */
    public function structured_data(): array
    {
        $data = [
            '@context' => 'https://schema.org',
            '@type' => 'PropertyValue',
            'name' => $this->value(),
            'description' => $this->description(),
            'url' => $this->url()
        ];

        if ($this->property()) {
            $data['property'] = [
                '@type' => 'Property',
                'name' => $this->property()->name()
            ];
        }

        return $data;
    }

    /**
     * Analytics: usage_count
     * Returns the number of products using this option
     *
     * @return int The usage count
     */
    public function usage_count(): int
    {
        return $this->_option->products_count ?? 0;
    }

    /**
     * Analytics: is_active
     * Returns whether the option is active
     *
     * @return bool Whether the option is active
     */
    public function is_active(): bool
    {
        return (bool)($this->_option->active ?? true);
    }

    /**
     * Analytics: is_default
     * Returns whether this is the default option
     *
     * @return bool Whether this is the default option
     */
    public function is_default(): bool
    {
        return (bool)($this->_option->default ?? false);
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array<string, mixed> The array representation
     * @throws Error
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'value' => $this->value(),
            'description' => $this->description(),
            'url_handle' => $this->url_handle(),
            'url' => $this->url(),
            'property' => $this->property()?->toArray(),
            'has_image' => $this->has_image(),
            'image' => $this->image()->toArray(),
            'handle' => $this->handle(),
            'title' => $this->title(),
            'metafields' => $this->metafields(),
            'published_at' => $this->published_at()?->toArray(),
            'updated_at' => $this->updated_at()?->toArray(),
            'sort_order' => $this->sort_order(),
            'meta_title' => $this->meta_title(),
            'meta_description' => $this->meta_description(),
            'canonical_url' => $this->canonical_url(),
            'robots' => $this->robots(),
            'structured_data' => $this->structured_data(),
            'usage_count' => $this->usage_count(),
            'is_active' => $this->is_active(),
            'is_default' => $this->is_default()
        ];
    }
}
