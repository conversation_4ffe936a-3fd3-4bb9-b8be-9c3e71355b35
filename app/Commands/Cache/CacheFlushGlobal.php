<?php

declare(strict_types=1);

namespace App\Commands\Cache;

use App\Commands\TimeInfoCommand;

/**
 * Class CacheFlushGlobal
 *
 * @package App\Commands\Cache
 */
class CacheFlushGlobal extends TimeInfoCommand
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $signature = 'cache:flush-global';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Flush MongoDB Global cache (collection "cache_global")';

    /**
     * Execute the console command.
     *
     * @return void
     * @throws \Exception
     */
    public function handle(): void
    {
        $this->info('Flushing MongoDB Global cache ...');

        \Cache::driver('mongodb')->flush();
        \Cache::driver('mongodb-remote')->flush();

        $this->info('Done!');
    }
}
