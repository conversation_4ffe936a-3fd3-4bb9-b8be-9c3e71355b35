<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 20.12.2018 г.
 * Time: 16:21 ч.
 */

namespace App\Commands\ProductsTemp;

use App\Commands\Db\Migrate\MigrateCommand;
use App\Console\Traits\TargetSites;
use App\Models\Router\Site;
use Illuminate\Console\Command;
use MetaData;
use Throwable;
use Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * @deprecated
 */
class VariantsListingCommand extends Command
{
    use TargetSites;

    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'products:tmp-variants-listing';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate table structure if not migrated';

    /**
     * Execute the console command.
     * @return void
     * @throws Throwable
     */
    public function handle(): void
    {
        $this->exec(function ($site): void {
            MigrateCommand::registerDoctrineHelpers();

            $this->executeForSite($site);
        });
    }

    /**
     * @param \App\Models\Router\Site $site
     * @return mixed
     */
    protected function executeForSite(Site $site)
    {
        $tables = array_map(function($item) {
            return reset($item);
        }, \Illuminate\Support\Facades\DB::select('SHOW TABLES LIKE "products_lists_temp_v1%"'));

        foreach ($tables as $tableName) {
            if (Schema::hasTable($tableName)) {
                $s = microtime(true);
                Schema::table($tableName, function (Blueprint $table) use ($tableName): void {
                    if (MetaData::getSchema($tableName)->getColumn('id')->key == 'PRI') {
                        $table->dropPrimary('id');
                        $table->unsignedInteger('id')->unsigned()->index()->change();
                    }

                    if (MetaData::getSchema($tableName)->getColumn('url_handle')->key == 'UNI') {
                        $table->dropUnique('uk_url_handle');
                        $table->index('url_handle', 'idx_url_handle');
                    }

                    for ($i = 1; $i <= 3; $i++) {
                        if (!Schema::hasColumn($tableName, 'v' . $i . '_id')) {
                            $table->unsignedInteger('v' . $i . '_id')->default(0);
                        }
                    }

                    if (!Schema::hasColumn($tableName, 'variant_id')) {
                        $table->unsignedInteger('variant_id')->default(0);
                    }

                    if (!Schema::hasColumn($tableName, 'parameter_type')) {
                        $table->enum('parameter_type', ['simple','main', 'variant'])
                            ->default('simple')->index('idx_parameter_type');
                    }

                    if (MetaData::getSchema($tableName)->getColumn('v1_id') && MetaData::getSchema($tableName)->getColumn('v1_id')->key != 'UNI') {
                        $table->unique(['id', 'v1_id', 'v2_id', 'v3_id'], 'uk_id_variant_values');
                    }
                });

                $this->warn(sprintf('Temp table %s regenerate for site ID: %d for %s sec.', $tableName, $site->site_id, round(microtime(true) - $s, 3)));
            }
        }
    }

}
