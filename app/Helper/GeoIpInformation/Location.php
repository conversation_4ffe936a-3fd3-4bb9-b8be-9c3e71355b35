<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 24.8.2017 г.
 * Time: 11:19 ч.
 */

namespace App\Helper\GeoIpInformation;

use App\Helper\TimeZone;

class Location extends AbstractPlace
{
    /**
     * @var string
     */
    protected $averageIncome;

    /**
     * @var string
     */
    protected $accuracyRadius;

    /**
     * @var string
     */
    protected $latitude;

    /**
     * @var string
     */
    protected $longitude;

    /**
     * @var string
     */
    protected $metroCode;

    /**
     * @var string
     */
    protected $populationDensity;

    /**
     * @var string
     */
    protected $postalCode;

    /**
     * @var string
     */
    protected $postalConfidence;

    /**
     * @var string
     */
    protected $timeZone;

    /**
     * @return string|null
     */
    public function getAverageIncome()
    {
        return $this->averageIncome;
    }

    /**
     * @param string $averageIncome
     * @return $this
     */
    public function setAverageIncome($averageIncome): static
    {
        $this->averageIncome = $averageIncome;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getAccuracyRadius()
    {
        return $this->accuracyRadius;
    }

    /**
     * @param string $accuracyRadius
     * @return $this
     */
    public function setAccuracyRadius($accuracyRadius): static
    {
        $this->accuracyRadius = $accuracyRadius;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getLatitude()
    {
        return $this->latitude;
    }

    /**
     * @param string $latitude
     * @return $this
     */
    public function setLatitude($latitude): static
    {
        $this->latitude = $latitude;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getLongitude()
    {
        return $this->longitude;
    }

    /**
     * @param string $longitude
     * @return $this
     */
    public function setLongitude($longitude): static
    {
        $this->longitude = $longitude;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getMetroCode()
    {
        return $this->metroCode;
    }

    /**
     * @param string $metroCode
     * @return $this
     */
    public function setMetroCode($metroCode): static
    {
        $this->metroCode = $metroCode;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getPopulationDensity()
    {
        return $this->populationDensity;
    }

    /**
     * @param string $populationDensity
     * @return $this
     */
    public function setPopulationDensity($populationDensity): static
    {
        $this->populationDensity = $populationDensity;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getPostalCode()
    {
        return $this->postalCode;
    }

    /**
     * @param string $postalCode
     * @return $this
     */
    public function setPostalCode($postalCode): static
    {
        $this->postalCode = $postalCode;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getPostalConfidence()
    {
        return $this->postalConfidence;
    }

    /**
     * @param string $postalConfidence
     * @return $this
     */
    public function setPostalConfidence($postalConfidence): static
    {
        $this->postalConfidence = $postalConfidence;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getTimeZone()
    {
        if (!$this->timeZone && $this->getLatitude() && $this->getLongitude()) {
            $this->timeZone = TimeZone::findTimezoneByLocation($this->getLatitude(), $this->getLongitude());
        }

        return $this->timeZone;
    }

    /**
     * @param string $timeZone
     * @return $this
     */
    public function setTimeZone($timeZone): static
    {
        $this->timeZone = $timeZone;
        return $this;
    }
}
