<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Artisan;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Modules\Core\Core\Models\Translation;
use Modules\Core\Core\Traits\ChatGPTTranslator;

class VueTranslationsAiLabelsCommand extends Command
{
    use ChatGPTTranslator;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vue:translations:ai-labels {lang : Language for generate.}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Vue translations for labels by AI';

    protected $hashToKey;

    protected $lang;

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $lang = $this->input->getArgument('lang');
        $languages = array_keys(config('languages.languages', []));
        if ($lang !== 'all') {
            if (!in_array($lang, $languages)) {
                $this->error(sprintf('Language "%s" is not defined!', $lang));
                return;
            }

            $languages = [$lang];
        }

        $labels = Translation::whereIn('locale', array_unique(array_merge($languages, ['en'])))->get()
            ->groupBy('locale');

        foreach ($languages as $language) {
            if ($language == 'en') {
                $this->warn('EN translation is not allowed!');
                continue;
            }

            $this->generateLabels($language, $labels->get('en') ?: collect(), $labels->get($language) ?: collect());

            $this->info(sprintf('Begin commit for language "%s"', $language));
            Artisan::call('vue:translations:labels', [
                '--locale' => $language,
            ]);
        }
    }

    /**
     * @param mixed $language
     * @param Illuminate\Support\Collection $enTranslations
     * @param Illuminate\Support\Collection $otherTranslations
     * @return mixed
     */
    protected function generateLabels($language, Collection $enTranslations, Collection $otherTranslations)
    {
        if ($language === 'en') {
            return;
        }

        $enTranslations = $enTranslations->map(fn(Translation $translation) => array_merge(Arr::except($translation->getAttributes(), ['id']), [
            'compare' => $this->generateHash('en', $translation),
            'hash' => $this->generateHash($language, $translation),
        ]))->keyBy('compare');

        $otherTranslations = $otherTranslations->map(fn(Translation $translation) => array_merge($translation->getAttributes(), [
            'compare' => $this->generateHash('en', $translation)
        ]))->keyBy('compare');

        $diff = array_diff($enTranslations->keys()->all(), $otherTranslations->keys()->all());

        $enTranslations = $enTranslations->only($diff);
        $enTranslations->map(function (array $data): void {
            $this->hashToKey[$data['hash']] = $data['key'];
        });

        $labels = $enTranslations->pluck('text', 'hash');

        $labelsChunk = $labels->chunk(100);
        $total = 0;
        foreach ($labelsChunk as $chunk) {
            $chunk = $chunk->forget(Translation::whereIn('hash', $chunk->keys())->pluck('hash')->all());
            if ($chunk->isNotEmpty()) {
                $total += $chunk->count();
                $this->writeLabels($language, $chunk->all());
                $this->info(sprintf('Completed %d labels from %d for language "%s"', $total, $labels->count(), $language));
            }
        }

        $this->info(sprintf('Language "%s" is done!', $language));
    }

    /**
     * @param string $language
     * @param mixed $labels
     * @return mixed
     */
    protected function writeLabels(string $language, $labels)
    {
        $translation = $this->gptTranslateText($labels, 'en', $language);
        if ($translation['status'] === false) {
            throw new Exception($translation['message']);
        }

        $labels = $translation['texts'] ?? [];

        $labels = collect($labels)->map(function ($value, $hash) use ($language): array {
            [$md5, $locale, $requestGroup, $group] = explode('-', $hash);
            return [
                'key' => $this->hashToKey[$hash],
                'locale' => $locale,
                'group' => $requestGroup,
                'subGroup' => $group,
                'text' => $value,
                'hash' => $hash,
                'last_modified_from' => 'AI translate cli',
            ];
        });

        foreach ($labels as $label) {
            $translation = Translation::firstOrCreate([
                'hash' => $label['hash']
            ], $label);

            if ($translation->wasRecentlyCreated) {
                $this->info(sprintf('Successfully created "%s" for language "%s"!', $translation->text, $translation->locale));
            } else {
                $this->warn(sprintf('Label "%s" with key "%s" exists for language "%s"!', $translation->text, $translation->key, $translation->locale));
            }
        }
    }

    /**
     * @param string $language
     * @param \Modules\Core\Core\Models\Translation $translation
     * @return mixed
     */
    protected function generateHash(string $language, Translation $translation): string
    {
        return sprintf('%s-%s-%s-%s', md5($translation->key), $language, $translation->group, $translation->subGroup);
    }
}
