{include file="../layout/header.tpl"}
{$widget->utilities->homeRedirect()}
{$widget->setSeo("utilities")}

<!-- BEGIN: content -->
<main class="_content">
    <div class="_slider-wrapper">
        {include file="widgets/extra/carousel.tpl" widget=$widget->carousel}
    </div>

    <div class="_tabs">
        <div class="_tabs-nav">
            <div class="container">
                <div class="_tabs-nav-inner">
                    <div class="_tabs-nav-item js-tabs-nav-item current" data-target="#tab1">
                        {include file="widgets/extra/text.tpl" widget=$widget->textTab1}
                    </div>

                    <div class="_tabs-nav-item js-tabs-nav-item" data-target="#tab2">
                        {include file="widgets/extra/text.tpl" widget=$widget->textTab2}
                    </div>

                    <div class="_tabs-nav-item js-tabs-nav-item" data-target="#tab3">
                        {include file="widgets/extra/text.tpl" widget=$widget->textTab3}
                    </div>
                </div>
            </div>
        </div>

        <div class="_tabs-content">
            <div class="container">
                <div class="_tab js-tab" id="tab1">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="_showcase-products js-showcase-products">
                                {include file="widgets/product/productShowcase.tpl" showcase=$widget->tab1Showcase1 custom_labels=true product_bar=true image_size="600x600"}
                                <a class="_showcase-products-arrow _showcase-products-arrow-prev js-showcase-product-prev" href="#"><i class="fa fa-chevron-left"></i></a>
                                <a class="_showcase-products-arrow _showcase-products-arrow-next js-showcase-product-next" href="#"><i class="fa fa-chevron-right"></i></a>
                            </div>
                        </div>

                        <div class="col-sm-4">
                            <div class="_showcase-products js-showcase-products">
                                {include file="widgets/product/productShowcase.tpl" showcase=$widget->tab1Showcase2 custom_labels=true product_bar=true image_size="600x600"}
                                <a class="_showcase-products-arrow _showcase-products-arrow-prev js-showcase-product-prev" href="#"><i class="fa fa-chevron-left"></i></a>
                                <a class="_showcase-products-arrow _showcase-products-arrow-next js-showcase-product-next" href="#"><i class="fa fa-chevron-right"></i></a>
                            </div>
                        </div>

                        <div class="col-sm-4">
                            <div class="_showcase-products js-showcase-products">
                                {include file="widgets/product/productShowcase.tpl" showcase=$widget->tab1Showcase3 custom_labels=true product_bar=true image_size="600x600"}
                                <a class="_showcase-products-arrow _showcase-products-arrow-prev js-showcase-product-prev" href="#"><i class="fa fa-chevron-left"></i></a>
                                <a class="_showcase-products-arrow _showcase-products-arrow-next js-showcase-product-next" href="#"><i class="fa fa-chevron-right"></i></a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="_tab js-tab" id="tab2">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="_tabs-text">
                                {include file="widgets/extra/text.tpl" widget=$widget->tab2Text1}
                            </div>
                        </div>

                        <div class="col-sm-6">
                            <div class="_tabs-text">
                                {include file="widgets/extra/text.tpl" widget=$widget->tab2Text2}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="_tab js-tab" id="tab3">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="_showcase-products js-showcase-products">
                                {include file="widgets/product/productShowcase.tpl" showcase=$widget->tab1Showcase4 custom_labels=true product_bar=true image_size="600x600"}
                                <a class="_showcase-products-arrow _showcase-products-arrow-prev js-showcase-product-prev" href="#"><i class="fa fa-chevron-left"></i></a>
                                <a class="_showcase-products-arrow _showcase-products-arrow-next js-showcase-product-next" href="#"><i class="fa fa-chevron-right"></i></a>
                            </div>
                        </div>

                        <div class="col-sm-4">
                            <div class="_showcase-products js-showcase-products">
                                {include file="widgets/product/productShowcase.tpl" showcase=$widget->tab1Showcase5 custom_labels=true product_bar=true image_size="600x600"}
                                <a class="_showcase-products-arrow _showcase-products-arrow-prev js-showcase-product-prev" href="#"><i class="fa fa-chevron-left"></i></a>
                                <a class="_showcase-products-arrow _showcase-products-arrow-next js-showcase-product-next" href="#"><i class="fa fa-chevron-right"></i></a>
                            </div>
                        </div>

                        <div class="col-sm-4">
                            <div class="_showcase-products js-showcase-products">
                                {include file="widgets/product/productShowcase.tpl" showcase=$widget->tab1Showcase6 custom_labels=true product_bar=true image_size="600x600"}
                                <a class="_showcase-products-arrow _showcase-products-arrow-prev js-showcase-product-prev" href="#"><i class="fa fa-chevron-left"></i></a>
                                <a class="_showcase-products-arrow _showcase-products-arrow-next js-showcase-product-next" href="#"><i class="fa fa-chevron-right"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {if $widget->bannersHomePage->isEnabled() && !empty($widget->bannersHomePage->getBanners())}
        <div class="container">
            <div class="_section-separator">
                <div class="row">
                    <div class="col-sm-12">
                        {include file="widgets/extra/banner.tpl" widget=$widget->bannersHomePage}
                    </div>
                </div>
            </div>
        </div>
    {/if}

    {if $widget->homeText1->isEnabled()}
        <div class="_home-text1 _section-separator">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12">
                        {include file="widgets/extra/text.tpl" widget=$widget->homeText1 image=$widget->homeText1Background}
                    </div>
                </div>
            </div>
        </div>
    {/if}

    {if $widget->recentArticlesHome->isEnabled()}
        <div class="_latest-news-wrapper">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        {if W::blog()->isEnabled()}
                            <div class="_section-title">
                                <h2>{t}sf.global.latest.news{/t}</h2>
                            </div>
                            <div class="_latest-news">
                                {include file="widgets/blog/articles.tpl" widget=$widget->recentArticlesHome imgSize='600x600'}
                            </div>
                        {else}
                            {include file="../notifications/error-include.tpl" error="{t}sf.widget.blog.err.blog_is_disabled{/t}"}
                        {/if}
                    </div>
                </div>
            </div>
        </div>
    {/if}


</main><!--// END: content -->

{include file="../layout/footer.tpl"}