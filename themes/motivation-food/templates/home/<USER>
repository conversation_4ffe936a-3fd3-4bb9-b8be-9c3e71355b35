{include file="../layout/header.tpl"}

{$widget->utilities->homeRedirect()}
{$widget->setSeo("utilities")}
<!-- BEGIN: content -->
<div class="_content">
    {if $widget->carousel->isEnabled()}
        <div class="_slider-container _section-separator">
            {include file="widgets/extra/carousel.tpl" widget=$widget->carousel}
        </div>
    {/if}

    {if $widget->text1->isEnabled()}
        <div class="_grid-row _homepage-text _homepage-text1 _section-separator" style="background-image: url({include file="widgets/extra/backgroundImage.tpl" widget=$widget->text1Background only_src=true});">
            <div class="container">
                {include file="widgets/extra/text.tpl" widget=$widget->text1}
            </div>
        </div>
    {/if}

    <div class="container">
        {if $widget->showcaseBrands1->isEnabled()}
            <div class="_showcase-brands _section-separator">
                {if $widget->showcaseBrands1->getSetting('enable_slider')}
                    {include file="widgets/product/showcaseSlider.tpl" showcase=$widget->showcaseBrands1 image_size='600x600'}
                {else}
                    {include file="widgets/product/showcase.tpl" showcase=$widget->showcaseBrands1 image_size='600x600'}
                {/if}
            </div>
        {/if}

        {if $widget->showcaseCategories->isEnabled()}
            <div class="_showcase-categories _section-separator{if !$widget->showcaseCategories->getSetting('enable_slider')} js-showcase-categories{/if}">
                {if $widget->showcaseCategories->getSetting('enable_slider')}
                    {include file="widgets/product/showcaseSlider.tpl" showcase=$widget->showcaseCategories image_size='600x600'}
                {else}
                    {include file="widgets/product/showcase.tpl" showcase=$widget->showcaseCategories image_size='600x600'}
                {/if}
            </div>
        {/if}

        {$widgetShowcase1 = $widget->showcaseProducts1}
        {if $widgetShowcase1->isEnabled()}
            <div class="row">
                <div class="col-md-12">
                    {if $widget->showcaseProducts1->getSetting('enable_slider')}
                        {include file="../../widgets/product/productShowcaseSlider.tpl" custom_labels=true product_bar=true showcase=$widgetShowcase1 image_size='600x600' image_srcset=['1200' => '300x300', '992' => '300x300', '768' => '600x600', '320' => '600x600']}
                    {else}
                        {include file="../../widgets/product/productShowcase.tpl" custom_labels=true product_bar=true showcase=$widgetShowcase1 image_size='600x600' image_srcset=['1200' => '300x300', '992' => '300x300', '768' => '600x600', '320' => '600x600']}
                    {/if}
                </div>
            </div>
        {/if}

        {$widgetBanners1 = $widget->banners1}
        {if $widgetBanners1->isEnabled()}
            <div class="_banners-container _section-separator">
                {include file="widgets/extra/banner.tpl" widget=$widgetBanners1}
            </div>
        {/if}

        {$widgetShowcase2 = $widget->showcaseProducts2}
        {if $widgetShowcase2->isEnabled()}
            <div class="_showcase-product-container">
                <div class="row">
                    <div class="col-md-12">
                        {if $widget->showcaseProducts2->getSetting('enable_slider')}
                            {include file="../../widgets/product/productShowcaseSlider.tpl" custom_labels=true product_bar=true showcase=$widgetShowcase2 image_size='600x600' image_srcset=['1200' => '300x300', '992' => '300x300', '768' => '600x600', '320' => '600x600']}
                        {else}
                            {include file="../../widgets/product/productShowcase.tpl" custom_labels=true product_bar=true showcase=$widgetShowcase2 image_size='600x600' image_srcset=['1200' => '300x300', '992' => '300x300', '768' => '600x600', '320' => '600x600']}
                        {/if}
                    </div>
                </div>
            </div>
        {/if}
    </div>

    {if $widget->text2->isEnabled()}
        <div class="_homepage-text _homepage-text2 _section-separator" style="background-image: url({include file="widgets/extra/backgroundImage.tpl" widget=$widget->text2Background only_src=true});">
            <div class="container">
                {include file="widgets/extra/text.tpl" widget=$widget->text2}
            </div>
        </div>
    {/if}

    <div class="container">
        {if $widget->textbox1->isEnabled() || $widget->textbox2->isEnabled() || $widget->textbox3->isEnabled() || $widget->textbox4->isEnabled()}
            <div class="_text-boxes-container _section-separator js-text-boxes">
                <div class="_text-boxes">
                    {if $widget->textbox1->isEnabled()}
                        <div class="_text-box">
                            <div class="_text-box-main">
                                {include file="widgets/extra/text.tpl" widget=$widget->textbox1}
                            </div>

                            <div class="_text-box-tooltip">
                                {include file="widgets/extra/text.tpl" widget=$widget->textbox1_tooltip}
                            </div>
                        </div>
                    {/if}

                    {if $widget->textbox2->isEnabled()}
                        <div class="_text-box">
                            <div class="_text-box-main">
                                {include file="widgets/extra/text.tpl" widget=$widget->textbox2}
                            </div>

                            <div class="_text-box-tooltip">
                                {include file="widgets/extra/text.tpl" widget=$widget->textbox2_tooltip}
                            </div>
                        </div>
                    {/if}

                    {if $widget->textbox3->isEnabled()}
                        <div class="_text-box">
                            <div class="_text-box-main">
                                {include file="widgets/extra/text.tpl" widget=$widget->textbox3}
                            </div>

                            <div class="_text-box-tooltip">
                                {include file="widgets/extra/text.tpl" widget=$widget->textbox3_tooltip}
                            </div>
                        </div>
                    {/if}

                    {if $widget->textbox4->isEnabled()}
                        <div class="_text-box">
                            <div class="_text-box-main">
                                {include file="widgets/extra/text.tpl" widget=$widget->textbox4}
                            </div>

                            <div class="_text-box-tooltip">
                                {include file="widgets/extra/text.tpl" widget=$widget->textbox4_tooltip}
                            </div>
                        </div>
                    {/if}
                </div>

                <div class="_text-boxes-pagination">
                </div>
            </div>
        {/if}
    </div>

    {$widgetBanners2 = $widget->banners2}
    {if $widgetBanners2->isEnabled()}
        <div class="_banners-container _section-separator">
            {include file="widgets/extra/banner.tpl" widget=$widgetBanners2}
        </div>
    {/if}

    <div class="container">
        {$widgetShowcase3 = $widget->showcaseProducts3}
        {if $widgetShowcase3->isEnabled()}
            <div class="row">
                <div class="col-md-12">
                    {if $widget->showcaseProducts3->getSetting('enable_slider')}
                        {include file="../../widgets/product/productShowcaseSlider.tpl" custom_labels=true product_bar=true showcase=$widgetShowcase3 image_size='600x600' image_srcset=['1200' => '300x300', '992' => '300x300', '768' => '600x600', '320' => '600x600']}
                    {else}
                        {include file="../../widgets/product/productShowcase.tpl" custom_labels=true product_bar=true showcase=$widgetShowcase3 image_size='600x600' image_srcset=['1200' => '300x300', '992' => '300x300', '768' => '600x600', '320' => '600x600']}
                    {/if}
                </div>
            </div>
        {/if}

        {if $widget->blogHome->isEnabled()}
            <div class="_blog-showcase-container _section-separator">
                <div class="container">
                    <div class="_section-title">
                        <h2>{t}sf.blog.header.title.posts{/t}</h2>

                        <div class="_section-title-actions">
                            <a class="_button _button-secondary _button-small _button-small-ghost _mobile-hidden" href="{route('blog.list')}" class="_view-all">{t}sf.widget.extra.search.view_all{/t}</a>
                            <a class="_button _button-secondary _button-small _button-small-ghost _button-icon js-blog-showcase-prev" href="#"><i class="fa fa-angle-left"></i></a>
                            <a class="_button _button-secondary _button-small _button-small-ghost _button-icon js-blog-showcase-next" href="#"><i class="fa fa-angle-right"></i></a>
                        </div>
                    </div>

                    <div class="_blog-showcase js-blog-showcase">
                        {include file="widgets/blog/articles.tpl" widget=$widget->blogHome truncate=150 backgroundImage=true}
                    </div>
                </div>
            </div>
        {/if}

        {$widgetShowcase4 = $widget->showcaseProducts4}
        {if $widgetShowcase4->isEnabled()}
            <div class="row">
                <div class="col-md-12">
                    {if $widget->showcaseProducts4->getSetting('enable_slider')}
                        {include file="../../widgets/product/productShowcaseSlider.tpl" custom_labels=true product_bar=true showcase=$widgetShowcase4 image_size='600x600' image_srcset=['1200' => '300x300', '992' => '300x300', '768' => '600x600', '320' => '600x600']}
                    {else}
                        {include file="../../widgets/product/productShowcase.tpl" custom_labels=true product_bar=true showcase=$widgetShowcase4 image_size='600x600' image_srcset=['1200' => '300x300', '992' => '300x300', '768' => '600x600', '320' => '600x600']}
                    {/if}
                </div>
            </div>
        {/if}

        {$lastViewedProducts = $widget->lastViewedProducts}
        {if $lastViewedProducts->isEnabled() && !$lastViewedProducts->getProducts()->isEmpty()}
            <div class="row">
                <div class="col-md-12">
                    {include file="../../widgets/product/lastViewed.tpl" carousel=true showcase=$lastViewedProducts custom_labels=true product_bar=true showcase=$lastViewedProducts image_size='600x600' image_srcset=['1200' => '300x300', '992' => '300x300', '768' => '600x600', '320' => '600x600']}
                </div>
            </div>
        {/if}
    </div>

    {if $widget->text3->isEnabled()}
        <div class="_grid-row _homepage-text _homepage-text3 _section-separator" style="background-image: url({include file="widgets/extra/backgroundImage.tpl" widget=$widget->text3Background only_src=true});">
            {include file="widgets/extra/text.tpl" widget=$widget->text3}
        </div>
    {/if}

    {if $widget->showcaseBrands2->isEnabled()}
        <div class="container">
            <div class="_showcase-brands _section-separator">
                {if $widget->showcaseBrands2->getSetting('enable_slider')}
                    {include file="widgets/product/showcaseSlider.tpl" showcase=$widget->showcaseBrands2 image_size='600x600'}
                {else}
                    {include file="widgets/product/showcase.tpl" showcase=$widget->showcaseBrands2 image_size='600x600'}
                {/if}
            </div>
        </div>
    {/if}


</div><!--// END: content -->
{include file="../layout/footer.tpl"}