/*=============================================================================*\
    VARIABLES
\*=============================================================================*/

/*  FONTS
-------------------------------------------------------------------------------*/

/*  Fonts changeble */
@font-family-main: 'Noto Sans';
@font-family-titles: 'Noto Sans';
@font-family-buttons: 'Noto Sans';
@font-family-product-list-title: 'Noto Sans';

@font-size-main: 14px;
@font-size-heading-1: 48px;
@font-size-heading-2: 30px;
@font-size-heading-3: 24px;
@font-size-heading-4: 20px;
@font-size-heading-5: 16px;
@font-size-heading-6: 14px;
@font-size-buttons: 14px;
@font-size-product-list-title: 14px;

@font-weight-main: 400;
@font-weight-titles: 700;
@font-weight-buttons: 400;
@font-weight-product-list-title: 400;

@font-style-main: normal;
@font-style-titles: normal;
@font-style-buttons: normal;
@font-style-product-list-title: normal;

/*  Family */
@font-awesome: 'FontAwesome', sans-serif;
@font-glyphicons: 'Glyphicons Halflings', sans-serif;

/*  Size */
@font-size-slider-nav: 68px;
@font-size-notification-icon: 32px;
@font-size-loader-list: 50px;

/*  Line height */
@line-height-base: 1.72;
@line-height-medium: 1.5;
@line-height-low: 1.2;
@line-height-button: 1.2;


/*  THEME COLORS
-------------------------------------------------------------------------------*/

/*  Main */
@color-main-background: #fff;
@color-main-borders: #e8e8e8;
@color-main-text: #666;
@color-main-strong-text: #333;
@color-main-meta-text: #999;
@color-main-titles: #7b2430;
@color-main-titles-second: #f17e01;
@color-main-highlight: #f17e01;
@color-main-highlight-secondary: #9dac07;
@color-main-icons: #f17e01;

@color-newsletter-text: #fff;
@color-blog-showcase-title: #fff;

/* Second */
@color-second-background: #fff;
@color-second-borders: #dbdbdb;
@color-second-text: #666;
@color-second-secondary-text: #333;
@color-second-meta-text: #999;
@color-second-titles: #333;
@color-second-highlight: #f17e01;
@color-second-highlight-secondary: #9dac07;
@color-second-image-box: #e8e8e8;

@color-second-title-background-start: #f79b3c;
@color-second-title-background-end: #f47415;
@color-second-title-text: #fff;

@color-second-title-secondary-background-start: #b0c609;
@color-second-title-secondary-background-end: #9dac07;
@color-second-title-secondary-text: #fff;

@color-second-button-background: #f17e01;
@color-second-button-borders: #f17e01;
@color-second-button-text: #fff;
@color-second-button-background-hover: #e17500;
@color-second-button-borders-hover: #e17500;
@color-second-button-text-hover: #fff;

@color-second-button-secondary-background: #fff;
@color-second-button-secondary-borders: #f17e01;
@color-second-button-secondary-text: #f17e01;
@color-second-button-secondary-background-hover: #e17500;
@color-second-button-secondary-borders-hover: #e17500;
@color-second-button-secondary-text-hover: #fff;

/*  Header */
@color-header-background: #fff;
@color-header-borders: #ebebeb;
@color-header-text: #666;
@color-header-highlight: #f17e01;

@color-header-icons: #a49997;
@color-header-icons-hover: #f17e01;
@color-header-icons-bubble-background: #7b2430;
@color-header-icons-bubble-text: #fff;

@color-navigation-text: #7b2430;
@color-navigation-icon: #7b2430;
@color-navigation-hover-background: #a49997;
@color-navigation-hover-text: #fff;

@color-dropdowns-background: #fff;
@color-dropdowns-borders: #e8e8e8;
@color-dropdowns-titles: #7b2430;
@color-dropdowns-text: #333;
@color-dropdowns-secondary-text: #666;
@color-dropdowns-meta-text: #999;
@color-dropdowns-highlight: #f17e01;
@color-dropdowns-image-box: #e8e8e8;

@color-dropdowns-button-background: #f17e01;
@color-dropdowns-button-borders: #f17e01;
@color-dropdowns-button-text: #fff;
@color-dropdowns-button-background-hover: #e17500;
@color-dropdowns-button-borders-hover: #e17500;
@color-dropdowns-button-text-hover: #fff;

@color-intro-background: #f17e01;
@color-intro-text: #fff;

/*  Footer */
@color-footer-background: #f2f1ef;
@color-footer-borders: #a49997;
@color-footer-text: #666;
@color-footer-titles: #7b2430;
@color-footer-highlight: #f17e01;

@color-footer-bottom-bar-background: #fff;
@color-footer-bottom-bar-text: #a49997;
@color-footer-bottom-bar-links: #7b2430;
@color-footer-bottom-bar-highlight: #f17e01;

@color-footer-socials-background: #f17e01;
@color-footer-socials-icon: #fff;
@color-footer-socials-icon-background: #f17e01;
@color-footer-socials-icon-hover: #f17e01;
@color-footer-socials-icon-background-hover: #fff;

/*  Slider */
@color-slider-background: #fff;
@color-slider-text: #fff;
@color-slider-titles: #7b2430;
@color-slider-arrows: #f17e01;
@color-slider-dots: #f17e01;

@color-slider-button-background: #f17e01;
@color-slider-button-borders: #f17e01;
@color-slider-button-text: #fff;
@color-slider-button-background-hover: #e17500;
@color-slider-button-borders-hover: #e17500;
@color-slider-button-text-hover: #fff;

/*  Product Listing */
@color-product-listing-background: #fff;
@color-product-listing-borders: #e8e8e8;
@color-product-listing-title: #333;
@color-product-listing-highlight: #f17e01;
@color-product-listing-category: #96989b;
@color-product-listing-price: #7b2430;
@color-product-listing-price-old: #96989b;
@color-product-listing-actions: #96989b;
@color-product-listing-actions-highlight: #f17e01;


/*  Product Details */
@color-product-images-background: #fff;
@color-product-images-borders: #fff;
@color-product-images-borders-highlight: #f17e01;
@color-product-price: #f17e01;
@color-product-price-old: #333;
@color-product-price-saved: #f17e01;
@color-product-gallery-background: #212331;

/*  Forms */
@color-forms-fields-background: #fff;
@color-forms-fields-borders: #e8e8e8;
@color-forms-fields-text: #999;
@color-forms-fields-placeholder: #ccc;
@color-forms-fields-icons: #f17e01;

@color-forms-checkbox-background: #fff;
@color-forms-checkbox-borders: #e8e8e8;
@color-forms-checkbox-check: #f17e01;

@color-forms-radio-background: #fff;
@color-forms-radio-borders: #e8e8e8;
@color-forms-radio-check: #f17e01;

@color-forms-range-slider: #e5e5e5;
@color-forms-range-slider-highlight: #bbbfc8;
@color-forms-range-slider-sliders: #f17e01;

/*  Buttons */
@color-button-background: #f17e01;
@color-button-borders: #f17e01;
@color-button-text: #fff;
@color-button-background-hover: #e17500;
@color-button-borders-hover: #e17500;
@color-button-text-hover: #fff;

@color-button-secondary-background: #fff;
@color-button-secondary-borders: #f17e01;
@color-button-secondary-text: #f17e01;
@color-button-secondary-background-hover: #e17500;
@color-button-secondary-borders-hover: #e17500;
@color-button-secondary-text-hover: #fff;

@color-button-thertiary-background: #fff;
@color-button-thertiary-borders: #f17e01;
@color-button-thertiary-text: #f17e01;

@color-button-thertiary-background-hover: #f17e01;
@color-button-thertiary-borders-hover: #f17e01;
@color-button-thertiary-text-hover: #fff;

@color-button-disabled-background: #f7f7f7;
@color-button-disabled-text: #ccc;

/*  Text sections */
@color-text1-background: #eee;
@color-text1-text: #666;
@color-text1-titles: #7b2430;
@color-text1-highlight: #f17e01;

/*  Promo bar */
@color-promo-bar-background: #f17e01;
@color-promo-bar-text: #fff;
@color-promo-bar-close: #fff;

@color-promo-bar-button-background: #f17e01;
@color-promo-bar-button-borders: #fff;
@color-promo-bar-button-text: #fff;
@color-promo-bar-button-background-hover: #e17500;
@color-promo-bar-button-borders-hover: #e17500;
@color-promo-bar-button-text-hover: #fff;

/*  Breadcrumb */
@color-breadcrumb-background: #f8f8f8;
@color-breadcrumb-text: #666;
@color-breadcrumb-text-active: #999;
@color-breadcrumb-text-hover: #f17e01;

/*  Pagination */
@color-pagination-borders: #e8e8e8;
@color-pagination-text: #666;
@color-pagination-highlight: #f17e01;
@color-pagination-disabled: #ccc;

/*  Tooltips */
@color-tooltips-background: #212331;
@color-tooltips-borders: #51546c;
@color-tooltips-text: #fff;

/*  Labels */
@color-label-new-background: #f17e01;
@color-label-new-text: #fff;

@color-label-sale-background: #a4b508;
@color-label-sale-text: #fff;

@color-label-discount-background: #e80101;
@color-label-discount-text: #fff;

@color-label-leasing-background: #f17e01;
@color-label-leasing-text: #fff;

@color-label-delivery-background: #7b2430;
@color-label-delivery-text: #fff;

@color-label-custom-background: #e80101;
@color-label-custom-text: #fff;

@color-label-featured-background: #f17e01;
@color-label-featured-text: #fff;

/*  Popups */
@color-popups-background: #fff;
@color-popups-borders: #e8e8e8;
@color-popups-text: #666;
@color-popups-titles: #333;
@color-popups-highlight: #f17e01;

@color-popups-button-background: #f17e01;
@color-popups-button-borders: #f17e01;
@color-popups-button-text: #fff;
@color-popups-button-background-hover: #e17500;
@color-popups-button-borders-hover: #e17500;
@color-popups-button-text-hover: #fff;

@color-popups-button-secondary-background: #fff;
@color-popups-button-secondary-borders: #f17e01;
@color-popups-button-secondary-text: #f17e01;
@color-popups-button-secondary-background-hover: #e17500;
@color-popups-button-secondary-borders-hover: #e17500;
@color-popups-button-secondary-text-hover: #fff;


/*  STATIC COLORS
-------------------------------------------------------------------------------*/

/*  Error Text */
@color-error-text: #ed1c24;

/*  Notifications */
@color-notification-text: #ff0000;
@color-notification-background: #fff;
@color-notification-border: #ff0000;

@color-notification-note-text: #ffa800;
@color-notification-note-background: #fff;
@color-notification-note-border: #ffa800;

@color-notification-error-text: #ed1c24;
@color-notification-error-background: #fff;
@color-notification-error-border: #ed1c24;

@color-notification-success-text: #79b12d;
@color-notification-success-background: #fff;
@color-notification-success-border: #79b12d;

/*  Toastr */
@color-toastr-text: #ff0000;
@color-toastr-background: #fff;
@color-toastr-border: #ff0000;

@color-toastr-note-text: #fff;
@color-toastr-note-background: #ffa800;
@color-toastr-note-border: #ffa800;

@color-toastr-error-text: #fff;
@color-toastr-error-background: #ed1c24;
@color-toastr-error-border: #ed1c24;

@color-toastr-success-text: #fff;
@color-toastr-success-background: #79b12d;
@color-toastr-success-border: #79b12d;

/*  Product Status */
@color-status-instock-text: #fff;
@color-status-instock-background: #3c3;

@color-status-outofstock-text: #fff;
@color-status-outofstock-background: #f00;

@color-status-twodays-text: #fff;
@color-status-twodays-background: #f93;

@color-status-preorder-text: #fff;
@color-status-preorder-background: #3370cc;

@color-status-request-text: #fff;
@color-status-request-background: #c600d4;


/*  Z-INDEX
-------------------------------------------------------------------------------*/

@z-page-loader: 200;
@z-fixed-sidebar: 130;
@z-header-fixed: 120;
@z-compare: 110;
@z-to-top: 100;
@z-text-boxes: 90;
@z-logobar: 85;
@z-midbar: 80;
@z-topbar: 70;
@z-navbar: 60;
@z-slider: 30;
@z-loader: 20;


/*  OTHER
-------------------------------------------------------------------------------*/

@separator: 60px;
@separator-small: 30px;


/*  RATIO
-------------------------------------------------------------------------------*/

@golden-ratio-ngt: 0.618%;
@golden-ratio-pst: 1.618%;
@image-orientation: 100%;


/*  SCREEN
-------------------------------------------------------------------------------*/

/*  XXX small screen: phone */
@screen-xxxs: 320px;
@screen-xxxs-min: @screen-xxxs;
@screen-small-phone: @screen-xxxs-min;

/*  XX small screen: phone */
@screen-xxs: 375px;
@screen-xxs-min: @screen-xxs;
@screen-small-phone: @screen-xxs-min;

/*  Extra small screen: phone */
@screen-xs: 480px;
@screen-xs-min: @screen-xs;
@screen-phone: @screen-xs-min;

/*  Small screen: tablet */
@screen-sm: 768px;
@screen-sm-min: @screen-sm;
@screen-tablet: @screen-sm-min;

/*  Medium screen: tablet-landscape */
@screen-md: 992px;
@screen-md-min: @screen-md;
@screen-tablet-landscape: @screen-md-min;

/*  Medium screen: desktop */
@screen-desktop: 1025px;
@screen-desktop-min: @screen-desktop;

/*  Large screen: wide desktop */
@screen-lg: 1200px;
@screen-lg-min: @screen-lg;
@screen-lg-desktop: @screen-lg-min;

/*  So media queries don't overlap when required, provide a maximum */
@screen-xxxs-max: (@screen-xxs-min - 1);
@screen-xxs-max: (@screen-xs-min - 1);
@screen-xs-max: (@screen-sm-min - 1);
@screen-sm-max: (@screen-md-min - 1);
@screen-md-max: (@screen-lg-min - 1);
@screen-tablet-max: (@screen-desktop-min - 1);

@hover: ~'(min-width: @{screen-desktop})';