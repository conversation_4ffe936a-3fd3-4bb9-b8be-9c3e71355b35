{$properties = W::categoryProperties()->getForCurrentCategory()}

{if $properties->count()}
    {if false}
        <style>
            ._filter-category-properties ._checkbox .checker, ._filter-category-properties ._checkbox .checker input,
            ._filter-category-properties ._checkbox .checker span {
                top: 10px!important;
            }
        </style>
    {/if}
    <div class="_filter-category-properties">
        {foreach W::categoryProperties()->getForCurrentCategory() as $property}
            <div class="_filter-category-property {if $property->active_value}js-has-checked open{/if}" data-filter-box="p-{$property->id}">
                <div class="_filter-category-property-title js-section-title js-filter-category-property-toggle active">
                    <h5>{$property->name}</h5>
                    <span class="_collapse"></span>
                    {if false}
                        {if $property->show_in_filters == 'both'}
                            {if $property->hasImage()}
                                <img src="{$property->getImage('150x150')}"
                                     alt="{$property->name}"
                                     title="{$property->name}"
                                     style="vertical-align: middle; max-width: 50px; max-height: 50px;" />
                            {/if}
                            <h5>{$property->name}</h5>
                        {elseif $property->show_in_filters == 'image' && $property->hasImage()}
                            <img src="{$property->getImage('150x150')}"
                                 alt="{$property->name}"
                                 title="{$property->name}"
                                 style="vertical-align: middle; max-width: 50px; max-height: 50px;" />
                        {else}
                            <h5>{$property->name}</h5>
                        {/if}
                        <span class="_collapse"></span>
                    {/if}
                </div>

                <form action="" class="_form js-filter-box open">
                    {if $property->type == 'checkbox'}
                        <div class="_form-inner js-property-filter">
                            {foreach $property->options as $option}
                                <div class="_form-row">
                                    <div class="_form-col">
                                        <label class="_checkbox">
                                            <input type="checkbox" {if $option->active_checkbox}checked{/if} data-vendor-handle="{W::categoryProperties()->getVendorHandle()}" data-type="checkbox" data-category-handle="{W::categoryProperties()->getCategoryHandle()}" data-property-name="{$property->url_handle}" value="{$option->url_handle}" data-type="checkbox" class="category-property-filter" data-uicontrol="uniform">
                                            {$option->value}
                                        </label>
                                        {if false}
                                            <label class="_checkbox">
                                                <input type="checkbox"
                                                       {if $option->active_checkbox}checked{/if}
                                                       data-vendor-handle="{W::categoryProperties()->getVendorHandle()}"
                                                       data-type="checkbox"
                                                       data-category-handle="{W::categoryProperties()->getCategoryHandle()}"
                                                       data-property-name="{$property->url_handle}"
                                                       value="{$option->url_handle}"
                                                       data-type="checkbox"
                                                       class="category-property-filter"
                                                       data-uicontrol="uniform">
                                                <span class="_form-col">
                                            {if $option->show_in_filters == 'both'}
                                                {if $option->hasImage()}
                                                    <img src="{$option->getImage('150x150')}"
                                                         alt="{$option->value}" title="{$option->value}" style="vertical-align: middle;" />
                                                {/if}
                                                {$option->value}
                                            {elseif $option->show_in_filters == 'image' && $option->hasImage()}
                                                <img src="{$option->getImage('150x150')}"
                                                     alt="{$option->value}"
                                                     title="{$option->value}"
                                                     style="vertical-align: middle; max-width: 25px; max-height: 25px;" />
                                            {else}
                                                {$option->value}
                                            {/if}
                                        </span>
                                            </label>
                                        {/if}
                                    </div>
                                </div>
                            {/foreach}
                        </div>
                    {elseif $property->type == 'range'}
                        {include file="widgets/category/property/range-filter.tpl"}
                    {/if}
                </form>
            </div>
        {/foreach}
    </div>
{/if}