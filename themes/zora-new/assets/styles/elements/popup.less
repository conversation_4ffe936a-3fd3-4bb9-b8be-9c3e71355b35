/*=============================================================================*\
    POPUP
\*=============================================================================*/

/*  MODAL - BOOTSTRAP
-------------------------------------------------------------------------------*/

.modal-dialog {
    margin: 30px auto;
    .modal-content {
        border: none;
        border-radius: 0;
        .modal-body {
            padding: 20px;
            .close {
                position: absolute;
                top: 0;
                right: 0;
                width: 30px;
                height: 30px;
                opacity: 1;
                &:before {
                    font-family: FontAwesome;
                    content: '\f00d';
                    font-size: @font-size-xlarge;
                    color: @color-main-meta-text; /* theme */
                    font-weight: 300;
                    .centerer(true, true);
                }
            }
        }
        .modal-footer {
            padding: 20px;
            border-top: 1px dotted @color-main-borders; /* theme */
        }
    }
}

/*  POPUP
-------------------------------------------------------------------------------*/

._popup {
    ._popup-title {
        padding-bottom: 20px;
        margin-bottom: 20px;
        border-bottom: 1px solid @color-main-borders; /* theme */
        h4 {
            &:extend(h2 all);
        }
    }
}