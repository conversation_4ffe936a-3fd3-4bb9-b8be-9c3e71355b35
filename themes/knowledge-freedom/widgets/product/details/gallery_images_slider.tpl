    {$has_360_or_video = $product->images->whereIn('type', ['video', '360-gallery'])->isNotEmpty()}
    {if $has_360_or_video}
        {include file="widgets/product/details/gallery_images_slider_360_video.tpl" image_size="1280x1280" thumbItem=5 vertical="false"}
    {else}

    {if (!isset($initSlider))}
        {$initSlider = false}
    {/if}

    {if empty($image_size)}
        {$image_size = '800x800'}
    {/if}

    {if empty($thumb_size)}
        {$thumb_size = '150x150'}
    {/if}

    {if $product->images->count() > 1}
        <div class="js-gallery _product-details-gallery{if ($initSlider)} swiper-container{/if}"{if isset($sliderDirection)} data-direction="{$sliderDirection}"{/if}{if isset($sliderSpeed)} data-speed="{$sliderSpeed}"{/if}{if isset($sliderAutoplay)} data-autoplay="{$sliderAutoplay}"{/if}{if isset($sliderEffect)} data-effect="{$sliderEffect}"{/if}{if isset($sliderSlidesPerView)} data-slides-per-view="{$sliderSlidesPerView}"{/if}{if isset($defaultSpaceBetweenItems)} data-default-space-between-items="{$defaultSpaceBetweenItems}"{/if}{if isset($breakpoints)} data-breakpoints="{$breakpoints}"{/if}{if isset($itemsPerBreakpoint)} data-items-per-breakpoint="{$itemsPerBreakpoint}"{/if}{if isset($spaceBetweenItems)} data-space-between-items="{$spaceBetweenItems}"{/if}{if isset($sliderLoop)} data-loop="{$sliderLoop}"{/if}{if isset($onSlideChangeStart)} data-on-slide-change-start="{$onSlideChangeStart}"{/if} {if isset($simulateTouch)} data-simulate-touch="{$simulateTouch}"{/if}{if isset($autoHeight)} data-auto-height="{$autoHeight}"{/if}{if $rtl}dir="rtl"{/if}>
            <ul id="productDetailsGallery{$product->getPrimaryImageUniqueId()}"{if ($initSlider)} class="swiper-wrapper"{/if}>
                {foreach $product->images as $product_image}
                    <li class="{if ($initSlider)} swiper-slide{/if}{if $product_image->global} __product_image{/if}">
                        <a id="img{$product_image->id}" href="javascript:void(0);" data-image="{$product_image->link[{$image_size}]}" data-large="{$product_image->link['original']}" data-large-width="{$product_image->max_thumb_size|default}">
                            <img src="{$product_image->link[{$thumb_size}]}"
                                 alt="{if $product_image->name}{$product_image->name}{else}{$product->name}{/if}"
                                 title="{if $product_image->name}{$product_image->name}{else}{$product->name}{/if}"
                            >
                        </a>
                    </li>
                {/foreach}
            </ul>
            <div class="loader-container">
                <div class="loader">
                    <span></span>
                    <span></span>
                    <span></span>
                    <span></span>
                    <span></span>
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>

        {if ($initSlider)}
            {if (isset($sliderPagination))}
                <div class="swiper-pagination"></div>
            {/if}

            {if (!isset($prevButtonMarkup))}
                {if isset($sliderDirection) && $sliderDirection == 'vertical'}
                    {$prevButtonMarkup = '<i class="fa fa-angle-up"></i>'}
                {else}
                    {$prevButtonMarkup = '<i class="fa fa-angle-left"></i>'}
                {/if}
            {/if}

            {if (!isset($nextButtonMarkup))}
                {if isset($sliderDirection) && $sliderDirection == 'vertical'}
                    {$nextButtonMarkup = '<i class="fa fa-angle-down"></i>'}
                {else}
                    {$nextButtonMarkup = '<i class="fa fa-angle-right"></i>'}
                {/if}
            {/if}

            {if (isset($sliderNavigation))}
                <div class="swiper-button swiper-button-prev">{$prevButtonMarkup nofilter}</div>
                <div class="swiper-button swiper-button-next">{$nextButtonMarkup nofilter}</div>
            {/if}

            {if (isset($sliderScrollbar))}
                <div class="swiper-scrollbar"></div>
            {/if}
        {/if}
    {/if}
{/if}