/*=============================================================================*\
    HEADER
\*=============================================================================*/

._header {
	background-color: @color-header-background; /* theme */
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 200;
}

._topbar {
	background-color: @color-topbar-background; /* theme */
	border-width: 0;
	border-bottom-width: 1px;
	border-style: solid;
	border-color: @color-topbar-borders; /* theme */
	position: relative;
	z-index: 5;

	&-inner {
		display: table;
		width: 100%;
		height: 30px;
	}
}

._utilities {
	align-self: center;
	width: 160px;
	text-align: right;
	font-size: 0;
	line-height: 0;
}

._navbar {
    background-color: @color-header-background; /* theme */
	border-width: 0;
	border-bottom-width: 1px;
	border-style: solid;
	border-color: @color-header-borders; /* theme */
	perspective: 1000px;
    position: relative;
    z-index: 4;

	&-inner {
		display: flex;
		justify-content: space-between;
		width: 100%;
	}

	[class^="col-"] {
		position: static;
	}
}

._navbar-mobile-button {
    margin: 18px 0;
    margin-left: 20px;
    width: 29px;
    align-self: center;
    display: none;
}