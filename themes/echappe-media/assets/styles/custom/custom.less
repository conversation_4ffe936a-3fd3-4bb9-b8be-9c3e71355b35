@media (max-width: 1200px) {
  .no-mobile {
    display: none;
  }
}

@media (min-width: 1200px) {
  .no-desktop {
    display: none;
  }
}

._contact-map {
  display: none;
}

._footer-cols ._text h6 {
  font-size: 13px;
  font-weight: 600;
}

@media (min-width: 1200px) {
  ._header .container {
    width: 95%;
  }

  ._content > ._breadcrumb-container:first-child {
    margin-top: -25px;
  }
}

@media (min-width: 1200px) {
  ._logo img {
    max-height: 70px;
  }
}

._logo {
  width: 210px;
}

._navigation li, ._navigation-footer li a {
  font-size: 13px !important;
}

._carousel-buttons {
  position: absolute;
  top: -2px;
  right: 50%;
  transform: translateX(50%);
}

._text .table-responsive:last-child, ._text .textbox-iframe:last-child, ._text ol:last-child, ._text p:last-child, ._text ul:last-child, ._textbox .table-responsive:last-child, ._textbox .textbox-iframe:last-child, ._textbox ol:last-child, ._textbox p:last-child, ._textbox ul:last-child {
  text-transform: none;
}

@media (max-width: 991px) {
  .mobile-padding {
    padding-left: 20px !important;
    padding-right: 20px !important;
  }

  .section-even .row {
    display: flex;
    flex-direction: column;
  }

  .section-even .row:before {
    content: none;
  }

  .section-even .row [class*=col-md]:first-child {
    order: 2;
    margin-top: 30px;
  }

  .section-even .row [class*=col-md]:nth-child(2) {
    order: 1;
  }
}