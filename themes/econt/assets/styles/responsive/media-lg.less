/*=============================================================================*\
    LARGE DEVICES / DESKTOPS
\*=============================================================================*/

@media (max-width: 1500px) {
	._to-top {
		right: 30px;
		bottom: 30px;
	}

	._compare-products {
		~ ._to-top {
			bottom: 70px;
		}

		&.hide {
			~ ._to-top {
				bottom: 10px;
			}
		}
	}
}

@media (max-width: @screen-lg-min) {
	._categories-menu {
		width: 212.5px
	}

	._navigation-main-list-item {
		margin-left: 2px;
		margin-right: 6px;

		&:before {
			left: -6px;
		}
	}

	._categories-menu-dropdown {
		._filter-categories-list {
			> ul {
				> li {
					&.item-collapse {
						> a {
							&:before {
								top: 12px
							}
						}
					}

					> a {
						padding-top: 12px;
						padding-bottom: 10px;
					}

					> ul {
						width: @menu-dropdown-width-small + @menu-dropdown-horizontal-offsets*2 + @menu-dropdown-border-width*2;

					&.width-2 {
						width: @menu-dropdown-width-small*2 + @menu-dropdown-horizontal-offsets*3 + @menu-dropdown-border-width*2;
					}

					&.width-3 {
						width: @menu-dropdown-width-small*3 + @menu-dropdown-horizontal-offsets*4 + @menu-dropdown-border-width*2;
					}

						> li {
							width: @menu-dropdown-width-small;
						}
					}
				}
			}
		}
	}

	.slide-html {
		padding-left: 300px;
	}

	/* #Content
	-------------------------------------------------------------------------------*/

	._showcase-brands {
		._showcase-item {
			width: 235px;
		}
	}

	._text-boxes-container {
		overflow: hidden;
	}

	._text-boxes {
		._text-box {
			width: 33.33%;
		}
	}

	._product-details-gallery {
		.swiper-wrapper {
			height: @gallery-thumb-size*4 + @gallery-thumb-offset*3;
		}
	}
}