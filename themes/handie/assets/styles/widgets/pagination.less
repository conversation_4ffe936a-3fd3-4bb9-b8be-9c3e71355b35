/*=============================================================================*\
    PAGINATION
\*=============================================================================*/

._pagination {
	._section-separator();
	font-size: 0;
    text-align: center;
    position: relative;
    z-index: 1;

    .pagination {
        display: inline-block;
        border-radius: 0;
        border: none;
        //margin-top: 80px;

        li {
            display: inline-block;
            width: 32px;
            height: 32px;

            a {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                font-size: @pagination-size;
                color: @color-button-second-text; /* theme */
                border: none;
                background: @color-button-second-background; /* theme */

                @media (min-width: @screen-desktop) {
                    &:hover {
                        background-color: @color-button-background; /* theme */
                        color: @color-main-accent-text; /* theme */
                    }
                }
            }

            &.first,
            &.last {
                display: none;
            }

            &.prev,
            &.next {
                a {
                    font-size: 0;
                    text-indent: -4000px;

                    & when (@rtl) {
                        text-indent: 4000px;
                    }

                    &:before {
                        pointer-events: none;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        position: absolute;
                        left: 0;
                        top: 0;
                        width: 32px;
                        height: 32px;
                        font-size: @pagination-size;
                        color: @color-button-second-text; /* theme */
                        text-indent: 0;
                    }
                    @media (min-width: @screen-desktop) {
                        &:hover {
                            &:before {
                              color: @color-main-accent-text; /* theme */
                            }
                        }
                    }
                }
            }

            &.prev {
                a {
                    &:before {
                        content:"\00ab";
                    }
                }
            }

            &.next {
                a {
                    &:before {
                        content:"\00bb";
                    }
                }
            }

            &.disabled {
                a {
                    &:before {
                        color: @color-main-text; /* theme */
                    }
                    &:hover {
                        background: @color-button-second-background; /* theme */
                        &:before {
                            color: @color-button-second-text; /* theme */
                        }
                    }
                }
                &.active a {
                    background: @color-button-disable-background; /* theme */
                }
            }

            &.active {
                a {
                    background-color: @color-button-background; /* theme */
                    color: @color-main-accent-text; /* theme */
                }
            }

            & + li {
                margin-left: 1px;
            }
        }
    }
}