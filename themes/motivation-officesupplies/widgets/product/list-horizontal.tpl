{if !empty($showcase)}
    {$widget = $showcase}
{/if}

{if !$widget instanceof \App\Helper\Widget}
    {$grid_class = $widget->getClassName()}
{/if}

{if !empty($widget_products)}
    {$grid_class = Widget::get('product.filters')->getClassName()}
    {$widget = $widget_products}
{/if}

{$is_default_content = false}
{if !empty($widget) && $widget->isDefaultContent()}
    {$is_default_content = true}
{/if}

{if isset($image_size)}
    {$image_size = $image_size}
{else}
    {$image_size = '600x600'}
{/if}

{$show_quick_view = setting('show_product_quick_view', 'yes') == 'yes'}

{if !isset($product_name_tag)}
    {$product_name_tag = 'h3'}
{/if}

{if empty($product_bar)}
    {$product_bar = false}
{/if}

{if !empty($widget->getC<PERSON>Name())}
    {$grid_class = $widget->getClassName()}
{/if}

{$list_widget_settings = Widget::get('product.filters')->getSettings()}
{if !$products->isEmpty()}
    {strip}

    <div class="_products-list{if !$list_widget_settings['listing_show_buy']} no-button{/if}{if !$list_widget_settings['show_quick_view']} no-quick-view{/if}{if $is_default_content} _has-helper{/if}{if $list_class|default} {$list_class|default}{/if}{if $list_horizontal_size|default} {$list_horizontal_size|default}{/if}{if W::filters()->perRowMobile()|default == 1} _per-row-mobile-one{else} _per-row-mobile-two{/if}">
        {foreach $products as $product}
            <div class="_product product-details-js {$grid_class|default} {if $product->overlay}overlay-product-disable-sell{/if}" data-box="product" data-product-id="{$product->id}">

                <div class="_product-inner">
                    <div class="_product-image">
                        <a class="_product-image-thumb" href="{$product->url()}">
                            <span class="_product-image-thumb-holder">
                                <img src="{noImage($image_size)}"

                                     class="lazyload-image lazyload-{$product->getOrientation()}"
                                     data-src="{$product->getImage($image_size)}"
                                     data-first-src="{$product->getImage($image_size)}"
                                     {if $product->image_second}data-second-src="{$product->image_second->getImage($image_size)}" {/if}
                                     alt="{$product->image->name|default:$product->name|default}"
                                     title="{$product->image->name|default:$product->name|default}"
                                     {if $product->aspect_ratio}
                                         style="aspect-ratio: {$product->aspect_ratio};"
                                     {/if}
                                     width="100%"
                                     height="auto"
                                >
                            </span>
                        </a>

                        {if $list_widget_settings['show_quick_view']}
                            <a class="_product-quick-view" href="{$product->url()}" data-ajax-modal="true" data-modal-class="product-details">
                                <span>
                                    <i class="fa fa-search" title="{t}sf.widget.product.act.quick_view{/t}"></i>
                                </span>
                            </a>
                        {/if}

                        {if $product->new === 'yes' || $product->sale === 'yes' || $product->featured || $custom_labels|default || (isset($product->discount) && $discount_inside|default)}
                            <div class="_product-ribbon-holder">
                                {if $product->new === 'yes'}
                                    <span class="_product-ribbon _product-ribbon-new">{t}sf.global.label.new{/t}</span>
                                {/if}
                                {if $product->sale === 'yes'}
                                    <span class="_product-ribbon _product-ribbon-sale">{t}sf.global.label.on_sale{/t}</span>
                                {/if}
                                {if isset($product->discount) && $discount_inside|default}
                                    <span {if $product->discount->style}style="{$product->discount->style}"{/if} class="_product-ribbon _product-ribbon-discount _product-ribbon-discount-{$product->discount->type}" title="{t}sf.widget.product.tip.discount_target{/t}: {$product->discount->target_type}"><span class="rtl-ltr">-&nbsp;{$product->discount->type_value_formatted nofilter}</span></span>
                                {/if}
                                {if $product->featured}
                                    <span class="_product-ribbon _product-ribbon-featured">{t}sf.global.label.featured{/t}</span>
                                {/if}
                                {if $custom_labels|default}
                                    {if $product->free_shipping}
                                        <span class="_product-ribbon _product-ribbon-delivery">{t}sf.global.free.delivery{/t}</span>
                                    {/if}
                                    {foreach from=$product->labels item=label}
                                        <span class="_product-ribbon _product-ribbon-custom" {if $label->style}style="{$label->style}"{/if}>{$label->name nofilter}</span>
                                    {/foreach}
                                {/if}
                            </div>
                        {/if}

                        {if isset($product->discount) && !$discount_inside|default}
                            {include file="widgets/product/discount_amount_in_label.tpl"}
                        {/if}

                        {foreach from=$product->banners item=pbanner}
                            <span class="_product-ribbon-banner {$pbanner->banner_position}">
                                <img class="_product-ribbon-banner-image lazyload-image lazyload-{$pbanner->getOrientation()}"

                                     src="{$pbanner->getImage($image_size)}"
                                     data-src="{$pbanner->getImage($image_size)}"
                                     title="{$pbanner->name|default}" alt="{$pbanner->name|default}">
                            </span>
                        {/foreach}
                    </div>

                    <div class="_product-info">
                        <div class="_product-name">
                            <{$product_name_tag} class="_product-name-tag">
                                <a href="{$product->url()}">{if !empty($truncate_names)}{$product->name|truncate:$truncate_names:"...":true}{else}{$product->name}{/if}</a>
                            </{$product_name_tag}>
                        </div>

                        {if $product->variant->unit|default && $product->variant->unit_text}
                            <div class="_product-unit-text">
                                <span class="_button _button_unit">{$product->variant->unit_text}</span>
                            </div>
                        {/if}

                        {if Apps::installed('product_review') && Apps::enabled('product_review') && Apps::setting('product_review', 'show_in_listing')}
                            <div class="_product-review">{include file="_global/templates/product/rating/rating_line.tpl" is_js=false}</div>
                        {/if}

                        {if $show_vendor|default && isset($product->vendor)}
                            <div class="_product-vendor">
                                <p>{$product->vendor->name}</p>
                            </div>
                        {/if}

                        {if !empty($product->short_description) && $list_widget_settings['show_short_description']}
                            <div class="_product-short-description">
                                <div class="_textbox">
                                    {$product->short_description nofilter}
                                </div>
                            </div>
                        {/if}



                        {if 0 && $product->category_properties|default && $list_widget_settings['enable_category_properties']}
                            {* disabled for now *}
                            <div class="_product-properties">
                                <ul>
                                    {foreach $product->category_properties as $property}
                                        <li>
                                            <span class="_product-properties-title">{$property['name']}:</span>
                                            <span class="_product-properties-value">{$property['value']}</span>
                                        </li>
                                    {/foreach}
                                </ul>
                            </div>
                        {/if}
                        {* end new short decription & category properties *}

                        {* {if $product->colors && $product->colors->isNotEmpty()}
                            <div class="_product-list-colors">
                                <ul>
                                    {foreach $product->colors as $color}
                                        <li style="background-color: {$color->color}" title="{$color->name}">&nbsp;</li>
                                    {/foreach}
                                </ul>
                            </div>
                        {/if} *}
                    </div>

                    {if !$show_variants|default}
                        {if $show_vendor_logo|default && $product->vendor && $product->vendor->image}
                            <div class="_product-list-vendor-image">
                                <a href="{$product->vendor->url}">
                                    <img src="{$product->vendor->getImage('150x150')}" alt="{$product->vendor->name}">
                                </a>
                            </div>
                        {/if}

                        <div class="_product-bar">
                            {if $list_widget_settings['listing_show_wishlist']}
                                <div class="_product-bar-col _product-bar-col-favorite">
                                    <a class="_product-add-to-favorite{if $product->favorite} active{/if}" href="#" data-id="{$product->id}" data-widget="product-wishlist">
                                            <span class="_figure-stack">
                                                <i class="_figure-stack-icon fa fa-heart"></i>
                                                {if !$product->favorite}
                                                    <span class="_figure-stack-label" data-action="text">{t}sf.global.add.to.favorites{/t}</span>
                                                {else}
                                                    <span class="_figure-stack-label" data-action="text">{t}sf.global.remove.from.favorites{/t}</span>
                                                {/if}
                                            </span>
                                    </a>
                                </div>
                            {/if}
                            {if Widget::has('productCompare') && $list_widget_settings['listing_show_compare']}
                                <div class="_product-bar-col _product-bar-col-compare">
                                    <div class="_product-compare js-product-compare">
                                        {include file="widgets/compare/add.tpl" product=$product}
                                    </div>
                                </div>
                            {/if}
                        </div>
                    {/if}

                    {if ($list_widget_settings['listing_show_price'] || $list_widget_settings['listing_show_buy']) && !$show_variants|default}
                    <div class="_product-options">
                        {if $list_widget_settings['listing_show_price'] && showPriceForUser()}
                            <div class="_product-price{if $product->price_from_discounted} has-discount{/if}">
                                <div class="_product-price-inner">
                                    {if !empty($product->price_from_discounted_units_formatted)}
                                        <span class="_product-price-compare">{if $product->diff_prices}<span class="_product-price-diff">{t}sf.product.price_from{/t} </span>{/if}{$product->price_from_discounted_units_formatted nofilter}</span>
                                        <del class="_product-price-old">{$product->price_from_units_formatted nofilter}</del>
                                    {else}
                                        <span class="price">{if $product->diff_prices}<span class="_product-price-diff">{t}sf.product.price_from{/t} </span>{/if}{$product->price_from_units_formatted nofilter}</span>
                                    {/if}
                                </div>
                            </div>
                        {/if}

                        {include file="widgets/product/listBuyButton.tpl"}
                    </div>
                {/if}

                    {if $show_variants|default}
                        <div class="_product-variants-wrap">
                            {if $show_vendor_logo|default && $product->vendor && $product->vendor->image}
                                <div class="_product-list-vendor-image">
                                    <a href="{$product->vendor->url}">
                                        <img src="{$product->vendor->getImage('150x150')}" alt="{$product->vendor->name}">
                                    </a>
                                </div>
                            {/if}
                            <div class="_price-wrap">
                                <div class="_product-countdown">
                                    {include file="widgets/common/countdown.tpl" endDate="{$product->countDownList}"}
                                </div>
                                {include file="./../../templates/products/details-price-listing.tpl"}
                            </div>
                            {if ($product_bar == true) && ($list_widget_settings['listing_show_wishlist'] || $list_widget_settings['listing_show_compare'])}
                                <div class="_product-bar">
                                    {if $list_widget_settings['listing_show_wishlist']}
                                        <div class="_product-bar-col _product-bar-col-favorite">
                                            <a class="_product-add-to-favorite{if $product->favorite} active{/if}" href="#" data-id="{$product->id}" data-widget="product-wishlist">
                                                <span class="_figure-stack">
                                                    <i class="_figure-stack-icon fa fa-heart"></i>
                                                    {if !$product->favorite}
                                                        <span class="_figure-stack-label" data-action="text">{t}sf.global.add.to.favorites{/t}</span>
                                                    {else}
                                                        <span class="_figure-stack-label" data-action="text">{t}sf.global.remove.from.favorites{/t}</span>
                                                    {/if}
                                                </span>
                                            </a>
                                        </div>
                                    {/if}
                                    {if Widget::has('productCompare') && $list_widget_settings['listing_show_compare']}
                                        <div class="_product-bar-col _product-bar-col-compare">
                                            <div class="_product-compare js-product-compare">
                                                {include file="widgets/compare/add.tpl" product=$product}
                                            </div>
                                        </div>
                                    {/if}
                                </div>
                            {/if}
                            <form class="_product-variants-in-listings-form add-to-cart-form-js js-form-submit-ajax" action="{route('cart.add')}" method="POST">
                                {include file="widgets/product/details/choose_variant.tpl" quantity_uicontrol="spinner" select_uicontrol="select2" printLabels=true tooltips=true}
                            </form>
                        </div>
                    {/if}
                </div>
                {if Apps::enabled('yotpo') && Apps::setting('yotpo', 'show_in_listing')}
                    {\App\Models\System\AppsManager::getManager('yotpo')->render($product, true) nofilter}
                {/if}
            </div>
        {/foreach}
        {if $is_default_content}
            {include file="widgets/common/helper.tpl"}
        {/if}
        </div>
    {/strip}
{else}
    <div class="_notification">
        <p>{t}sf.widget.product.warn.no_products{/t}</p>
    </div>
{/if}
{if !empty($widget_products)}
    {if !empty(W::filters()->getCategory())}
        {include file="widgets/common/microdata/category.tpl" widget=$widget_products}
    {/if}
    {if !empty(W::filters()->getVendor())}
        {include file="widgets/common/microdata/vendor.tpl" widget=$widget_products}
    {/if}
{/if}
{if Apps::installed('store_locations')}
    <script type="text/javascript">
        $.get('/store_locations/change_button', function (text) {
            if (text && text.listing) {
                $('._product-add').html(text.listing);
            }
            $('.js-product-fixed-treshold').removeClass('hidden')
        });
    </script>
{/if}

