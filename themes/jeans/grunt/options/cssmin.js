module.exports = function (grunt, data) {
	return {
		build: {
		    options: {
                keepSpecialComments: 0
            },
			files: {
				'<%= _PATH.pub_styles %>/<%= _FILE.styles_min %>': ['<%= _PATH.pub_styles %>/<%= _FILE.styles %>']
			}
		},
		buildRTL: {
            options: {
                keepSpecialComments: 0
            },
			files: {
				'<%= _PATH.pub_styles %>/<%= _FILE.styles_rtl_min %>': ['<%= _PATH.pub_styles %>/<%= _FILE.styles_rtl %>']
			}
		}
	}
};
