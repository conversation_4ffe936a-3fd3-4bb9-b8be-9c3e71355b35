<div class="bs-docs-section">
  <h1 id="labels" class="page-header">Labels</h1>

  <h2>Example</h2>
  <div class="bs-example" data-example-id="labels-in-headings">
    <h1>Example heading <span class="label label-default">New</span></h1>
    <h2>Example heading <span class="label label-default">New</span></h2>
    <h3>Example heading <span class="label label-default">New</span></h3>
    <h4>Example heading <span class="label label-default">New</span></h4>
    <h5>Example heading <span class="label label-default">New</span></h5>
    <h6>Example heading <span class="label label-default">New</span></h6>
  </div>
{% highlight html %}
<h3>Example heading <span class="label label-default">New</span></h3>
{% endhighlight %}

  <h2>Available variations</h2>
  <p>Add any of the below mentioned modifier classes to change the appearance of a label.</p>
  <div class="bs-example" data-example-id="label-variants">
    <span class="label label-default">Default</span>
    <span class="label label-primary">Primary</span>
    <span class="label label-success">Success</span>
    <span class="label label-info">Info</span>
    <span class="label label-warning">Warning</span>
    <span class="label label-danger">Danger</span>
  </div>
{% highlight html %}
<span class="label label-default">Default</span>
<span class="label label-primary">Primary</span>
<span class="label label-success">Success</span>
<span class="label label-info">Info</span>
<span class="label label-warning">Warning</span>
<span class="label label-danger">Danger</span>
{% endhighlight %}

  <div class="bs-callout bs-callout-info" id="callout-labels-inline-block">
    <h4>Have tons of labels?</h4>
    <p>Rendering problems can arise when you have dozens of inline labels within a narrow container, each containing its own <code>inline-block</code> element (like an icon). The way around this is setting <code>display: inline-block;</code>. For context and an example, <a href="https://github.com/twbs/bootstrap/issues/13219">see #13219</a>.</p>
  </div>
</div>
