/*=============================================================================*\
    SIDEBAR
\*=============================================================================*/
._content-box {
    ._navigation-links {
        font-weight: @black;
        -ltr-text-transform: uppercase;

        ul {

            li {
                display: block;

                & + li {
                    margin-left: 0;
                    margin-top: 9px;
                }

                a {
                    color: @color-main-text; /* theme */

                    .hover({
                        color: @color-main-highlight; /* theme */
                    });
                }

                &.active {
                    a {
                        color: @color-main-highlight; /* theme */
                    }
                }
            }
        }
    }
}

._sidebar {
    ._block-title {
        margin-bottom: 0;
    }

    ._main-title {
        h1, h2, h3, h4, h5, h6 {
            font-size: @font-size-heading-4; /* theme */
        }
    }
}