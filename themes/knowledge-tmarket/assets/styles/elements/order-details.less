/*=============================================================================*\
    ORDER DETAILS
\*=============================================================================*/

/* Meta */
._order-details-meta {
	margin-bottom: 33px;
	.clearfix();
}

._order-details-meta-item {
	float: left;
	width: 50%;
	padding-right: 15px;
	margin-bottom: 30px;
	line-height: @line-height-medium;

	&:nth-child(2n+1) {
		clear: both;
	}

	&:last-child {
		width: 100%;
		margin-bottom: 0;
	}
}

._order-details-meta-title {
	margin-bottom: 8px;
	.uppercase();
}

._order-details-payments {
	table {
		width: 100%;
		border-collapse: collapse;
		white-space: nowrap;
	}

	tr {
		&.active {
			._order-details-button {
				.fa {
					&:before {
						content: '\f077';
					}
				}
			}
		}
	}

	th {
		border-top: 1px solid;
	}

	th,
	td {
		border-bottom: 1px solid;
		border-color: @color-main-borders; /* theme */
		padding: 8px;

		&:first-child {
			padding-left: 0;
		}

		&:last-child {
			padding-right: 0;
		}
	}

	.text-alignright {
		text-align: right;
	}
}

/* Product */
._order-details-product {
	border-bottom: 1px solid;
	border-bottom-color: @color-main-borders; /* theme */
	padding-bottom: 33px;
	margin-bottom: 33px;
	.clearfix();

	&:last-child {
		margin-bottom: 0;
	}
}

._order-details-product-image {
	float: left;
	width: 250px;
	margin-top: 6px;
	margin-right: 30px;
	text-align: center;
}

._order-details-product-info {
	overflow: hidden;
	color: @color-main-meta-text; /* theme */
}

._order-details-product-title {
	margin-bottom: 19px;
	.uppercase();
}

._order-details-product-parameters {
	font-size: calc(@font-size-main ~'-' 1px); /* theme */
	line-height: @line-height-medium;
}

._order-details-product-price-total {
	font-size: 24px;
	font-weight: bold;
	color: @color-product-price; /* theme */
	margin-top: 19px;
}

/* Summary */
._order-details-summary {
	padding: 15px 0;
	font-size: calc(@font-size-main ~'-' 2px); /* theme */
	color: @color-main-secondary-text; /* theme */
	text-align: right;
	.uppercase();

	ul {
		list-style-type: none;
	}

	li {
		display: table;
		width: 300px;
		margin-left: auto;
	}
}

._order-details-summary-title {
	display: table-cell;
	vertical-align: top;
	text-align: left;
}

._order-details-summary-value {
	display: table-cell;
	vertical-align: top;
}

._order-details-summary-total {
	border-top: 1px solid;
	border-top-color: @color-main-borders; /* theme */
	font-size: calc(@font-size-main ~'+' 4px); /* theme */
	font-weight: bold;
}

._order-details-actions {
	border-top: 1px solid;
	border-color: @color-main-borders; /* theme */
	padding: 17px 0;
}

._order-details-row {
	._order-details {
		padding: 30px 0 15px;
	}
}

._order-details-button {
	display: inline-block;
	font-size: 18px;

	&.loading {
		-webkit-animation: button-spin 1s linear infinite;
				animation: button-spin 1s linear infinite;
		
		.fa {
			&:before {
				content:'\f021';
			}
		}
	}

	.fa {
		&:before {
			content: '\f078'
		}
	}
}