module.exports = function (grunt, data) {

	return {
		htmlHeader: {
			options: {
				data: {
					'styles_vendors': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_vendors %>',
					'styles_vendors_rtl': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_vendors_rtl %>',
					'styles_fonts_google': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_fonts_google %>',
					'styles_fonts_embed': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_fonts_embed %>',
					'styles_sprite_gif': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_sprite_gif %>',
					'styles_sprite_jpg': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_sprite_jpg %>',
					'styles_sprite_png': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_sprite_png %>',
					'styles_sprite_svg': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_sprite_svg %>',
					'styles_custom': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_custom %>',
					'styles_custom_rtl': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_custom_rtl %>',
					'styles_min': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_min %>',
					'styles_rtl_min': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_rtl_min %>'
				}
			},
			files: {
				'<%= _PATH.templates_layout_src %>/<%= _FILE.templates_header_assets %>': '<%= _PATH.patterns %>/<%= _FILE.ptn_template_header_assets %>'
			}
		},
		htmlFooter: {
			options: {
				data: {
                    'styles_vendors': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_vendors %>',
                    'styles_vendors_rtl': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_vendors_rtl %>',
                    'styles_fonts_google': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_fonts_google %>',
                    'styles_fonts_embed': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_fonts_embed %>',
                    'styles_sprite_gif': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_sprite_gif %>',
                    'styles_sprite_jpg': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_sprite_jpg %>',
                    'styles_sprite_png': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_sprite_png %>',
                    'styles_sprite_svg': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_sprite_svg %>',
                    'styles_custom': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_custom %>',
                    'styles_custom_rtl': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_custom_rtl %>',
                    'styles_min': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_min %>',
                    'styles_rtl_min': '{$img_url}<%= _PATH.pub_cdn_styles %>/<%= _FILE.styles_rtl_min %>',
					'images_sprite_svg': data.SETUP.theme.name + '/' + '<%= _PATH.templates_layout %>/<%= _FILE.images_sprite_svg %>',
					'scripts_vendors': '{$img_url}<%= _PATH.pub_cdn_scripts %>/<%= _FILE.scripts_vendors %>',
					'scripts_custom': '{$img_url}<%= _PATH.pub_cdn_scripts %>/<%= _FILE.scripts_custom %>',
					'scripts_min': '{$img_url}<%= _PATH.pub_cdn_scripts %>/<%= _FILE.scripts_min %>'
				}
			},
			files: {
				'<%= _PATH.templates_layout_src %>/<%= _FILE.templates_footer_assets %>': '<%= _PATH.patterns %>/<%= _FILE.ptn_template_footer_assets %>'
			}
		}
	}

};
