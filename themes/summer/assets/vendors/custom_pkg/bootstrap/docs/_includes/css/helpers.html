<div class="bs-docs-section">
  <h1 id="helper-classes" class="page-header">Helper classes</h1>

  <h3 id="helper-classes-colors">Contextual colors</h3>
  <p>Convey meaning through color with a handful of emphasis utility classes. These may also be applied to links and will darken on hover just like our default link styles.</p>
  <div class="bs-example" data-example-id="contextual-colors-helpers">
    <p class="text-muted"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, tellus ac cursus commodo, tortor mauris nibh.</p>
    <p class="text-primary">Nullam id dolor id nibh ultricies vehicula ut id elit.</p>
    <p class="text-success">Duis mollis, est non commodo luctus, nisi erat porttitor ligula.</p>
    <p class="text-info">Maecenas sed diam eget risus varius blandit sit amet non magna.</p>
    <p class="text-warning">Etiam porta sem malesuada magna mollis euismod.</p>
    <p class="text-danger">Donec ullamcorper nulla non metus auctor fringilla.</p>
  </div>
{% highlight html %}
<p class="text-muted">...</p>
<p class="text-primary">...</p>
<p class="text-success">...</p>
<p class="text-info">...</p>
<p class="text-warning">...</p>
<p class="text-danger">...</p>
{% endhighlight %}
  <div class="bs-callout bs-callout-info" id="callout-helper-context-color-specificity">
    <h4>Dealing with specificity</h4>
    <p>Sometimes emphasis classes cannot be applied due to the specificity of another selector. In most cases, a sufficient workaround is to wrap your text in a <code>&lt;span&gt;</code> with the class.</p>
  </div>
  <div class="bs-callout bs-callout-warning" id="callout-helper-context-color-accessibility">
    <h4>Conveying meaning to assistive technologies</h4>
    <p>Using color to add meaning only provides a visual indication, which will not be conveyed to users of assistive technologies – such as screen readers. Ensure that information denoted by the color is either obvious from the content itself (the contextual colors are only used to reinforce meaning that is already present in the text/markup), or is included through alternative means, such as additional text hidden with the <code>.sr-only</code> class.</p>
  </div>

  <h3 id="helper-classes-backgrounds">Contextual backgrounds</h3>
  <p>Similar to the contextual text color classes, easily set the background of an element to any contextual class. Anchor components will darken on hover, just like the text classes.</p>
  <div class="bs-example bs-example-bg-classes" data-example-id="contextual-backgrounds-helpers">
    <p class="bg-primary">Nullam id dolor id nibh ultricies vehicula ut id elit.</p>
    <p class="bg-success">Duis mollis, est non commodo luctus, nisi erat porttitor ligula.</p>
    <p class="bg-info">Maecenas sed diam eget risus varius blandit sit amet non magna.</p>
    <p class="bg-warning">Etiam porta sem malesuada magna mollis euismod.</p>
    <p class="bg-danger">Donec ullamcorper nulla non metus auctor fringilla.</p>
  </div>
{% highlight html %}
<p class="bg-primary">...</p>
<p class="bg-success">...</p>
<p class="bg-info">...</p>
<p class="bg-warning">...</p>
<p class="bg-danger">...</p>
{% endhighlight %}
  <div class="bs-callout bs-callout-info" id="callout-helper-bg-specificity">
    <h4>Dealing with specificity</h4>
    <p>Sometimes contextual background classes cannot be applied due to the specificity of another selector. In some cases, a sufficient workaround is to wrap your element's content in a <code>&lt;div&gt;</code> with the class.</p>
  </div>
  <div class="bs-callout bs-callout-warning" id="callout-helper-bg-accessibility">
    <h4>Conveying meaning to assistive technologies</h4>
    <p>As with <a href="#helper-classes-colors">contextual colors</a>, ensure that any meaning conveyed through color is also conveyed in a format that is not purely presentational.</p>
  </div>

  <h3 id="helper-classes-close">Close icon</h3>
  <p>Use the generic close icon for dismissing content like modals and alerts.</p>
  <div class="bs-example" data-example-id="close-icon">
    <p><button type="button" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></button></p>
  </div>
{% highlight html %}
<button type="button" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></button>
{% endhighlight %}


  <h3 id="helper-classes-carets">Carets</h3>
  <p>Use carets to indicate dropdown functionality and direction. Note that the default caret will reverse automatically in <a href="../components/#btn-dropdowns-dropup">dropup menus</a>.</p>
  <div class="bs-example" data-example-id="caret">
    <span class="caret"></span>
  </div>
{% highlight html %}
<span class="caret"></span>
{% endhighlight %}


  <h3 id="helper-classes-floats">Quick floats</h3>
  <p>Float an element to the left or right with a class. <code>!important</code> is included to avoid specificity issues. Classes can also be used as mixins.</p>
{% highlight html %}
<div class="pull-left">...</div>
<div class="pull-right">...</div>
{% endhighlight %}
{% highlight scss %}
// Classes
.pull-left {
  float: left !important;
}
.pull-right {
  float: right !important;
}

// Usage as mixins
.element {
  .pull-left();
}
.another-element {
  .pull-right();
}
{% endhighlight %}

  <div class="bs-callout bs-callout-warning" id="callout-helper-pull-navbar">
    <h4>Not for use in navbars</h4>
    <p>To align components in navbars with utility classes, use <code>.navbar-left</code> or <code>.navbar-right</code> instead. <a href="../components/#navbar-component-alignment">See the navbar docs</a> for details.</p>
  </div>


  <h3 id="helper-classes-center">Center content blocks</h3>
  <p>Set an element to <code>display: block</code> and center via <code>margin</code>. Available as a mixin and class.</p>
{% highlight html %}
<div class="center-block">...</div>
{% endhighlight %}
{% highlight scss %}
// Class
.center-block {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

// Usage as a mixin
.element {
  .center-block();
}
{% endhighlight %}


  <h3 id="helper-classes-clearfix">Clearfix</h3>
  <p>Easily clear <code>float</code>s by adding <code>.clearfix</code> <strong>to the parent element</strong>. Utilizes <a href="http://nicolasgallagher.com/micro-clearfix-hack/">the micro clearfix</a> as popularized by Nicolas Gallagher. Can also be used as a mixin.</p>
{% highlight html %}
<!-- Usage as a class -->
<div class="clearfix">...</div>
{% endhighlight %}
{% highlight scss %}
// Mixin itself
.clearfix() {
  &:before,
  &:after {
    content: " ";
    display: table;
  }
  &:after {
    clear: both;
  }
}

// Usage as a mixin
.element {
  .clearfix();
}
{% endhighlight %}


  <h3 id="helper-classes-show-hide">Showing and hiding content</h3>
  <p>Force an element to be shown or hidden (<strong>including for screen readers</strong>) with the use of <code>.show</code> and <code>.hidden</code> classes. These classes use <code>!important</code> to avoid specificity conflicts, just like the <a href="#helper-classes-floats">quick floats</a>. They are only available for block level toggling. They can also be used as mixins.</p>
  <p><code>.hide</code> is available, but it does not always affect screen readers and is <strong>deprecated</strong> as of v3.0.1. Use <code>.hidden</code> or <code>.sr-only</code> instead.</p>
  <p>Furthermore, <code>.invisible</code> can be used to toggle only the visibility of an element, meaning its <code>display</code> is not modified and the element can still affect the flow of the document.</p>
{% highlight html %}
<div class="show">...</div>
<div class="hidden">...</div>
{% endhighlight %}
{% highlight scss %}
// Classes
.show {
  display: block !important;
}
.hidden {
  display: none !important;
}
.invisible {
  visibility: hidden;
}

// Usage as mixins
.element {
  .show();
}
.another-element {
  .hidden();
}
{% endhighlight %}


  <h3 id="helper-classes-screen-readers">Screen reader and keyboard navigation content</h3>
  <p>Hide an element to all devices <strong>except screen readers</strong> with <code>.sr-only</code>. Combine <code>.sr-only</code> with <code>.sr-only-focusable</code> to show the element again when it's focused (e.g. by a keyboard-only user). Necessary for following <a href="../getting-started/#accessibility">accessibility best practices</a>. Can also be used as mixins.</p>
{% highlight html %}
<a class="sr-only sr-only-focusable" href="#content">Skip to main content</a>
{% endhighlight %}
{% highlight scss %}
// Usage as a mixin
.skip-navigation {
  .sr-only();
  .sr-only-focusable();
}
{% endhighlight %}


  <h3 id="helper-classes-image-replacement">Image replacement</h3>
  <p>Utilize the <code>.text-hide</code> class or mixin to help replace an element's text content with a background image.</p>
{% highlight html %}
<h1 class="text-hide">Custom heading</h1>
{% endhighlight %}
{% highlight scss %}
// Usage as a mixin
.heading {
  .text-hide();
}
{% endhighlight %}
</div>
