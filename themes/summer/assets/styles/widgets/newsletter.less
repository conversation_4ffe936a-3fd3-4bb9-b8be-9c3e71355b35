/*=============================================================================*\
    NEWSLETTER
\*=============================================================================*/

/*	MAIN
-------------------------------------------------------------------------------*/

._newsletter {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
}

._newsletter-title,
._newsletter-form,
._newsletter-description {
	width: 100%;
}

._newsletter-title {
	order: 1;

	h6 {
		._footer-title();
		margin-bottom: 20px;
	}
}

._newsletter-form {
	order: 2;

	._form-row {
		margin-bottom: 6px;
	}

    ._field-stack {
    	display: block;
    	position: relative;
    }

    ._field {
    	height: 36px;
    	padding-right: 36px;
    }

    ._button {
    	background: transparent; /* theme */
    	border: 0;
    	width: auto;
    	margin: 0;
    	color: @color-forms-fields-icons; /* theme */
		font-size: 14px; /* theme */
    	padding: 0;
    	right: 10px;
    	.centerer(false, true);

    	&:before {
    		content:"\f0e0";
    		font-family: @font-awesome;
    		font-size: 18px;
    	}

    	._figure-stack {
    		display: none;
    	}

    	@media @hover {
    		&:hover {
    			opacity: .7;
    		}
    	}
    }
}

._newsletter-description {
	order: 3;
}


/*	POPUP
-------------------------------------------------------------------------------*/

._newsletter-popup {
	&-title {
		margin-bottom: 2px;
		color: @color-popups-titles; /* theme */
		.uppercase();

		h6 {
			._h4();
		}
	}

	&-description {
		margin-bottom: 10px;
	}

	._field-stack-addon {
		padding-left: 10px;
	}

	._form-row {
		margin-bottom: 0;
	}
}