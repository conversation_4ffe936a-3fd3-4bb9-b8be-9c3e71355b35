/*=============================================================================*\
    BREADCRUMB
\*=============================================================================*/

._breadcrumb-container {
	background-color: @color-breadcrumb-background; /* theme */
	color: @color-breadcrumb-text; /* theme */
	padding: 13px 0;

	ul {
		text-align: center;
		font-size: 0;
		line-height: 0;
		list-style-type: none;
	}

	li {
		display: inline-block;
		font-size: calc(@font-size-main ~'-' 2px); /* theme */
		line-height: @line-height-low;

		&:after {
			content: '/';
			float: right;
			margin-left: 5px;
			margin-right: 5px;
		}
		
		&:last-child {
			&:after {
				display: none;
			}
		}

		&.active {
			color: @color-breadcrumb-text-active; /* theme */
		}
	}

	a {
		color: @color-breadcrumb-text; /* theme */

		&:hover {
			color: @color-breadcrumb-text-hover; /* theme */
			text-decoration: none;
		}
	}
}

._breadcrumb-wrapper {
	._breadcrumb-container();
	margin-bottom: @separator;

	._section-separator {
		margin: 0;
	}
}