---
description: covers application namespaces (site, sitecp, api2, etc.), code organization namespaces, namespace resolution, middleware mapping, and best practices for namespace-aware development in CloudCart's multi-tenant e-commerce platform.
globs: 
alwaysApply: false
---
# CloudCart Namespace Architecture Rule

## Description

This rule provides comprehensive guidelines for understanding and working with CloudCart's sophisticated namespace architecture. It covers application namespaces (site, sitecp, api2, etc.), code organization namespaces, namespace resolution, middleware mapping, and best practices for namespace-aware development in CloudCart's multi-tenant e-commerce platform.

## Overview

CloudCart uses a dual-namespace system: **Application Namespaces** that define execution contexts (admin panel, storefront, API, etc.) and **Code Organization Namespaces** that structure the codebase. Understanding this architecture is crucial for proper development in CloudCart.

## Application Namespaces

### 1. Core Application Namespaces

CloudCart defines several application namespaces, each with specific purposes and execution contexts:

| Namespace   | Description                                                                | Entry Point                    |
|-------------|----------------------------------------------------------------------------|--------------------------------|
| `site`      | Storefront - customer-facing e-commerce interface                         | [bootstrap/app.php](mdc:bootstrap/app.php) |
| `sitecp`    | Admin area - store management interface                                    | [bootstrap/app.php](mdc:bootstrap/app.php) |
| `api2`      | API endpoints for managing store data                                      | [api2/Bootstrap/app.php](mdc:api2/Bootstrap/app.php) |
| `builder`   | CloudCart CLI - command-line interface                                    | [artisan](mdc:artisan) |
| `facebook`  | Facebook webhook receiver                                                  | [facebook/index.php](mdc:facebook/index.php) |
| `payments`  | Payment server for processor callbacks                                    | [bootstrap/app.php](mdc:bootstrap/app.php) |
| `console`   | CloudCart internal control panel for staff                                | Internal |
| `resellers` | Reseller management                                                        | [resellers/Bootstrap/app.php](mdc:resellers/Bootstrap/app.php) |

### 2. Namespace Resolution

CloudCart automatically resolves the current application namespace based on the request context:

```php
// Function: getNamespaceFromRequestUri()
// Location: app/Helper/functions.php

function getNamespaceFromRequestUri(): string
{
    if (PHP_SAPI == 'cli') {
        return 'builder';
    }

    $host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? null;
    if ($host) {
        $host = explode(':', (string) $host)[0];
    }

    switch ($host) {
        case 'marketplace.ccdev.info':
        case 'marketplace.cloudcart.com':
            return 'marketplace';
        case 'console.ccdev.info':
        case 'console.cloudcart.com':
        case 'console-hc.cloudcart.com':
            return 'console';
        case 'facebook.ccdev.info':
        case 'facebook.cloudcart.com':
            return 'facebook';
        case 'payments.ccdev.info':
        case 'payments.cloudcart.com':
            return 'payments';
        default:
            $parts = explode('/', ltrim($_SERVER['REQUEST_URI'] ?? '', '/'));
            return match (array_shift($parts)) {
                'admin' => 'sitecp',
                default => 'site',
            };
    }
}

// Access current namespace
$currentNamespace = app_namespace(); // or app('namespace')
```

### 3. Namespace-Specific Configuration

Each namespace has its own configuration, middleware, and providers:

```php
// Namespace-specific middleware
// File: bootstrap/middlewareByNamespace/

// Namespace-specific providers  
// File: bootstrap/providersByNamespace/

// Example: Checking current namespace in code
if (app_namespace() === 'sitecp') {
    // Admin panel specific logic
} elseif (app_namespace() === 'site') {
    // Storefront specific logic
} elseif (app_namespace() === 'api2') {
    // API specific logic
}
```

## Code Organization Namespaces

### 1. Main Application Structure

```php
// Core Application
namespace App\Http\Controllers;
namespace App\Models;
namespace App\Services;
namespace App\Jobs;
namespace App\Events;
namespace App\Listeners;
namespace App\Exceptions;
namespace App\Providers;
namespace App\Helper;

// Namespace-specific Controllers
namespace App\Http\Controllers\Sitecp;    // Admin panel controllers
namespace App\Http\Controllers\Site;     // Storefront controllers
namespace App\Http\Controllers\Uptime;   // Monitoring controllers

// Request Classes
namespace App\Http\Request\Sitecp;       // Admin panel requests
namespace App\Http\Request\Site;         // Storefront requests
namespace App\Http\Request\Api;          // API requests
```

### 2. API2 Namespace Structure

```php
// API2 Application
namespace Api\Http\Controllers;
namespace Api\Http\Middleware;
namespace Api\Http\Requests;
namespace Api\Http\Resources;
namespace Api\Models;
namespace Api\Providers;

// API2 Modules
namespace Api\Modules\Product;
namespace Api\Modules\Order;
namespace Api\Modules\Customer;
namespace Api\Modules\Blog;
namespace Api\Modules\Campaigns;
```

### 3. Module System Namespaces

```php
// Core Modules
namespace Modules\Core;
namespace Modules\Core\Core\Http\Controllers;
namespace Modules\Core\Core\Http\Request;
namespace Modules\Core\Core\Models;
namespace Modules\Core\Requests;

// Application Modules
namespace Modules\Apps\Erp\Gensoft;
namespace Modules\Apps\Shippings\DPD;
namespace Modules\Apps\Payments\Stripe;
namespace Modules\Apps\Others\GoogleAnalytics;

// Marketing Modules
namespace Modules\Marketing\Campaign;
namespace Modules\Marketing\CartRules;

// CloudCart Capital
namespace Modules\CloudCartCapital;

// Other Modules
namespace Modules\CcAnalytics;
namespace Modules\CcBoarding;
namespace Modules\CcSegments;
namespace Modules\Cloudflare;
namespace Modules\Cloudio;
namespace Modules\CustomIntegrations;
```

### 4. Console Namespace Structure

```php
// Console Application
namespace Console\Http\Controllers;
namespace Console\Providers;
namespace Console\Exceptions;
namespace Console\Extensions;
namespace Console\Helper;
```

### 5. Resellers Namespace Structure

```php
// Resellers Application
namespace Resellers\Http\Controllers;
namespace Resellers\Providers;
namespace Resellers\Console;
namespace Resellers\Exceptions;
```

## Namespace-Aware Development Patterns

### 1. Namespace-Specific Service Providers

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Providers;

use App\Providers\RouteServiceProvider;
use Illuminate\Support\ServiceProvider;
use Illuminate\Routing\Router;

class YourModuleServiceProvider extends ServiceProvider
{
    protected $namespace = 'Modules\YourModule\Http\Controllers';

    public function boot(): void
    {
        $this->registerRoutes();
        $this->registerViews();
        $this->registerTranslations();
    }

    protected function registerRoutes(): void
    {
        $this->app->make('router')->group([
            'namespace' => $this->namespace,
            'middleware' => RouteServiceProvider::siteCpMiddleware()
        ], function (Router $route): void {
            if (app_namespace() === 'sitecp') {
                $this->loadRoutesFrom(__DIR__ . '/../routes/sitecp.php');
            }
            
            if (app_namespace() === 'api2') {
                $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');
            }
        });
    }

    protected function registerViews(): void
    {
        $this->app->make('view')->addNamespace('your_module', __DIR__ . '/../resources/views');
    }

    protected function registerTranslations(): void
    {
        $this->loadTranslationsFrom(__DIR__ . '/../resources/lang', 'your_module');
    }
}
```

### 2. Namespace-Aware Controllers

```php
<?php

declare(strict_types=1);

namespace App\Http\Controllers\Sitecp;

use App\Http\Controllers\Controller;

class YourController extends Controller
{
    public function index(): Response
    {
        // Automatically scoped to 'sitecp' namespace
        // Middleware, authentication, and permissions are namespace-aware
        
        return response()->view('admin.your-view');
    }
}

// API2 Controller
namespace Api\Http\Controllers;

class YourApiController extends Controller
{
    public function index(): JsonResponse
    {
        // Automatically scoped to 'api2' namespace
        // Different middleware, authentication, and response format
        
        return response()->json(['data' => []]);
    }
}
```

### 3. Namespace-Aware Middleware

```php
// Route definitions with namespace-specific middleware
Route::group([
    'namespace' => 'Sitecp', 
    'middleware' => \App\Providers\RouteServiceProvider::siteCpMiddleware()
], function () {
    Route::resource('products', ProductsController::class);
});

Route::group([
    'namespace' => 'Api', 
    'middleware' => ['auth:api', 'api.rate_limit']
], function () {
    Route::apiResource('products', ProductController::class);
});

Route::group([
    'namespace' => 'Site',
    'middleware' => \App\Providers\RouteServiceProvider::siteMiddleware()
], function () {
    Route::get('/', HomeController::class);
});
```

### 4. Namespace-Aware Models

```php
<?php

declare(strict_types=1);

namespace App\Models\Product;

use App\Models\Base\AbstractProducts;

class Product extends AbstractProducts
{
    // Model automatically scoped to current site context
    // Namespace-aware relationships and scopes
    
    public function scopeForCurrentNamespace($query)
    {
        if (app_namespace() === 'sitecp') {
            // Admin panel specific scoping
            return $query->withTrashed();
        } elseif (app_namespace() === 'site') {
            // Storefront specific scoping
            return $query->active()->published();
        }
        
        return $query;
    }
}
```

## Namespace Resolution Helpers

### 1. Core Helper Functions

```php
// Get current application namespace
$namespace = app_namespace();

// Get namespace-specific app instance
$app = app('namespace');

// Check if running in specific namespace
if (app_namespace() === 'sitecp') {
    // Admin panel logic
}

// Namespace-aware URL generation
$url = route('admin.products.index'); // Automatically scoped to current namespace

// Namespace-aware view rendering
return view('products.index'); // Looks in namespace-specific view paths
```

### 2. Namespace-Aware Configuration

```php
// Config files can be namespace-specific
$config = config('app.name'); // Gets namespace-specific config if available

// Namespace-specific cache keys
CcCache::set("namespace." . app_namespace() . ".key", 3600, $value, [app_namespace()]);

// Namespace-specific session data
session()->put('namespace_data', $data);
```

### 3. Namespace-Aware Database Connections

```php
// Models automatically use namespace-appropriate database connections
class Product extends Model
{
    // Connection resolved based on current namespace and site context
    protected $connection = 'mysql'; // Automatically scoped to current site
}

// Manual connection selection
$connection = DB::connection('site_' . site('site_id'));
```

## Module Development Patterns

### 1. Creating a New Module

```php
// Directory structure for a new module
modules/
├── YourModule/
│   ├── Providers/
│   │   └── YourModuleServiceProvider.php
│   ├── Http/
│   │   ├── Controllers/
│   │   │   ├── Admin/
│   │   │   └── Api/
│   │   ├── Requests/
│   │   └── Middleware/
│   ├── Models/
│   ├── Services/
│   ├── Events/
│   ├── Listeners/
│   ├── Jobs/
│   ├── routes/
│   │   ├── sitecp.php
│   │   ├── api.php
│   │   └── site.php
│   ├── resources/
│   │   ├── views/
│   │   └── lang/
│   ├── config/
│   └── database/
│       └── migrations/
```

### 2. Module Service Provider Template

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Routing\Router;

class YourModuleServiceProvider extends ServiceProvider
{
    protected $namespace = 'Modules\YourModule\Http\Controllers';

    public function register(): void
    {
        $this->mergeConfigFrom(__DIR__ . '/../config/your_module.php', 'your_module');
        
        $this->app->singleton('your_module', function () {
            return new YourModuleManager();
        });
    }

    public function boot(): void
    {
        $this->registerRoutes();
        $this->registerViews();
        $this->registerTranslations();
        $this->registerMigrations();
        $this->registerPublishing();
    }

    protected function registerRoutes(): void
    {
        $this->app->make('router')->group([
            'namespace' => $this->namespace,
            'middleware' => $this->getMiddleware()
        ], function (Router $route): void {
            $this->loadNamespaceSpecificRoutes();
        });
    }

    protected function getMiddleware(): array
    {
        return match (app_namespace()) {
            'sitecp' => \App\Providers\RouteServiceProvider::siteCpMiddleware(),
            'api2' => ['auth:api', 'api.rate_limit'],
            'site' => \App\Providers\RouteServiceProvider::siteMiddleware(),
            default => ['web']
        };
    }

    protected function loadNamespaceSpecificRoutes(): void
    {
        if (app_namespace() === 'sitecp') {
            $this->loadRoutesFrom(__DIR__ . '/../routes/sitecp.php');
        }
        
        if (app_namespace() === 'api2') {
            $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');
        }
        
        if (app_namespace() === 'site') {
            $this->loadRoutesFrom(__DIR__ . '/../routes/site.php');
        }
    }
}
```

## Testing Namespace-Aware Code

### 1. Namespace-Specific Tests

```php
<?php

declare(strict_types=1);

namespace Tests\Feature\Sitecp;

use Tests\TestCase;

class AdminProductsTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Set namespace for testing
        app()->instance('namespace', 'sitecp');
    }

    public function test_admin_can_view_products(): void
    {
        $this->actingAs($this->createAdmin());
        
        $response = $this->get(route('admin.products.index'));
        
        $response->assertStatus(200);
        $this->assertEquals('sitecp', app_namespace());
    }
}

// API Tests
namespace Tests\Feature\Api;

class ApiProductsTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        app()->instance('namespace', 'api2');
    }

    public function test_api_returns_products(): void
    {
        $response = $this->getJson('/api/products');
        
        $response->assertStatus(200);
        $this->assertEquals('api2', app_namespace());
    }
}
```

### 2. Namespace Mocking

```php
// Mock namespace for testing
public function test_namespace_specific_behavior(): void
{
    // Test sitecp behavior
    app()->instance('namespace', 'sitecp');
    $this->assertEquals('admin_behavior', $this->service->getBehavior());
    
    // Test site behavior
    app()->instance('namespace', 'site');
    $this->assertEquals('storefront_behavior', $this->service->getBehavior());
    
    // Test api2 behavior
    app()->instance('namespace', 'api2');
    $this->assertEquals('api_behavior', $this->service->getBehavior());
}
```

## Best Practices

### ✅ **Namespace Organization**
- Use application namespaces to separate execution contexts
- Use code organization namespaces to structure related functionality
- Follow CloudCart's established namespace patterns
- Keep namespace-specific logic isolated

### ✅ **Namespace-Aware Development**
- Always check current namespace when behavior differs between contexts
- Use namespace-specific middleware and authentication
- Implement namespace-aware routing and URL generation
- Design models and services to work across namespaces

### ✅ **Module Development**
- Create self-contained modules with proper namespace isolation
- Use service providers to register namespace-specific routes and services
- Implement namespace-aware configuration and resource loading
- Follow the established module directory structure

### ✅ **Testing**
- Write namespace-specific tests for different execution contexts
- Mock namespaces when testing namespace-dependent behavior
- Ensure proper namespace isolation in test environments
- Test cross-namespace functionality where applicable

### ✅ **Performance**
- Cache namespace resolution results when appropriate
- Use namespace-specific caching strategies
- Optimize namespace-aware database queries
- Minimize namespace checks in hot code paths

This comprehensive namespace rule provides developers with everything they need to understand and work effectively with CloudCart's sophisticated namespace architecture, ensuring proper separation of concerns and maintainable code organization.
