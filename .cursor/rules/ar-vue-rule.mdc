---
description: This rule provides comprehensive guidelines for Vue.js development within CloudCart's sitecp (admin panel). It covers the Model pattern for API calls, error handling, form components, translations, date formatting, and best practices for component reuse.
globs: 
alwaysApply: false
---
# Vue.js Development Rules for CloudCart SiteCP

This rule provides comprehensive guidelines for Vue.js development within CloudCart's sitecp (admin panel). It covers the Model pattern for API calls, error handling, form components, translations, date formatting, and best practices for component reuse.

## Settings Components Architecture

CloudCart uses a sophisticated settings system with two main approaches:

### A. SettingsWrapper + SettingsBox Pattern
Used for traditional settings pages with multiple grouped sections.

**SettingsWrapper** (`vuejs-sitecp/src/components/SettingsWrapper/SettingsWrapper.vue`):
- Main container component that handles page layout, breadcrumbs, loading states
- Includes **SubmitChanges** component that automatically:
  - Tracks changes to settings data
  - Shows a floating action bar when changes are detected
  - Provides Save and Revert buttons
  - Handles form submissions and validation errors

**SettingsBox** (`vuejs-sitecp/src/components/SettingsBox/SettingsBox.vue`):
- Renders grouped settings using the `boxes` array structure
- Each box contains fields with specific types and components

### B. SettingsBox Standalone Pattern
Used for simpler settings or specialized forms.

#### Box Structure for SettingsBox
```javascript
setupBoxes() {
  this.boxes = [
    {
      key: "connect",
      group: "algolia",
      title: "Algolia connect",
      titleHelp: {
        label: "Algolia connect",
        parameters: [],
      },
      infoTitle: "Algolia connect",
      infoDescription: "Algolia connect",
      editMethod: "slide", // or "inline"
      lockEditMethod: false,
      hideActions: true,
      isVisible: true,
      hr: "hide",
      credentials: true,
      fields: [
        {
          key: "appId",
          type: "string",
          label: "Application ID",
          disabled: false,
          readonly: false,
          help: null,
          inputType: "string",
          credentials: true,
        },
        {
          key: "apiKey", 
          type: "string",
          label: "Admin API Key",
          disabled: false,
          readonly: false,
          help: null,
          inputType: "string",
          credentials: true,
        }
      ],
    }
  ];
}
```

#### Field Types for SettingsBox
- `string` - Text input
- `switch` - Toggle switch
- `number` - Number input
- `select` - Dropdown select
- `html` - Custom component via `markRaw(Component)`

### C. SubmitChanges Component Features
The SubmitChanges component (`vuejs-sitecp/src/components/SubmitChanges.vue`) provides:

1. **Automatic Change Detection**: Compares current form data with original values
2. **Floating Action Bar**: Shows when changes are detected with:
   - Save button (calls provided save function)
   - Revert button (restores original values)
   - Loading states during submission
3. **Validation Error Handling**: Displays errors returned from API
4. **Ignored Fields**: Can exclude specific fields from change detection

#### SubmitChanges Usage Pattern
```javascript
// In component template
<SettingsWrapper
  v-model:boxes="boxes"
  v-model:responseErrors="responseErrors"
  v-model:settings="settings"
  :action-submit="submitData"
  :loading="loading"
  :submit-loader="submitLoader"
  :translationLabels="translations"
  setting-key="general"
/>

// The SubmitChanges is automatically included in SettingsWrapper
// and will track changes to the 'settings' object
```

## Settings Data Structure Patterns

### Theme Settings API Response Structure

#### Groups Endpoint Response (`/admin/api/theme-settings`)
```json
{
  "success": true,
  "data": {
    "groups": [
      {
        "id": "theme-info",
        "label": "theme_info", 
        "description": null,
        "settings_count": 0,
        "has_changes": false
      }
    ],
    "theme": {
      "id": 1,
      "name": "Vessel",
      "version": "1.0.0"
    },
    "meta": {
      "total_groups": 18,
      "has_schema": true,
      "last_updated": "2025-06-16T17:39:53+00:00"
    }
  }
}
```

#### Group Settings Endpoint Response (`/admin/api/theme-settings/{group}`)
```json
{
  "success": true,
  "data": {
    "group": {
      "id": "names-typography",
      "label": "Typography",
      "description": null
    },
    "settings": [
      {
        "id": "type_body_font",
        "type": "font_picker",
        "component": "FormSelect",
        "props": {
          "id": "type_body_font",
          "label": "Body",
          "placeholder": "",
          "info": null
        },
        "value": "red_hat_display_n4",
        "default": "work_sans_n4",
        "validation": [],
        "meta": {
          "label": "Body",
          "info": null,
          "required": false
        }
      }
    ],
    "meta": {
      "total_settings": 36,
      "has_changes": false
    }
  }
}
```

### Dynamic Group Icon Mapping
Since group IDs are dynamic per theme, use pattern matching for icons:

```javascript
// Icon mapping for common group patterns
const iconMap = {
  'logo': 'fas fa-image',
  'favicon': 'fas fa-image', 
  'colors': 'fas fa-palette',
  'typography': 'fas fa-font',
  'layout': 'fas fa-th-large',
  'animation': 'fas fa-magic',
  'badge': 'fas fa-award',
  'button': 'fas fa-hand-pointer',
  'cart': 'fas fa-shopping-cart',
  'drawer': 'fas fa-bars',
  'icon': 'fas fa-icons',
  'input': 'fas fa-edit',
  'popover': 'fas fa-comment',
  'price': 'fas fa-dollar-sign',
  'product': 'fas fa-th',
  'search': 'fas fa-search',
  'swatch': 'fas fa-swatchbook',
  'variant': 'fas fa-list-ul'
};

// Function to get icon based on group ID pattern
getGroupIcon(groupId) {
  for (const [pattern, icon] of Object.entries(iconMap)) {
    if (groupId.includes(pattern)) {
      return icon;
    }
  }
  return 'fas fa-cog'; // Default icon
}
```

## 1. Model Pattern for API Calls

**ALWAYS use the established Model pattern** for API calls instead of custom axios clients. The Model pattern is located in `vuejs-sitecp/src/js/Eloquent/Model.js` and provides a complete ORM-like interface.

### Basic Model Usage
```javascript
import Model from '@js/Eloquent/Model';

class ThemeSettings extends Model {
  constructor() {
    super("/admin/api/theme-settings");
  }
}

// Usage in component
data() {
  return {
    model: new ThemeSettings()
  };
}
```

### Available Model Methods
- `find(id)` - Retrieve single record
- `all()` - Retrieve all records
- `create(data)` - Create new record
- `update(id, data)` - Update existing record
- `delete(id)` - Delete record
- `reactive` properties for loading states and errors

## 2. Settings Components Architecture

CloudCart uses a sophisticated settings system with two main approaches:

### A. SettingsWrapper + SettingsBox Pattern
Used for traditional settings pages with multiple grouped sections.

**SettingsWrapper** (`vuejs-sitecp/src/components/SettingsWrapper/SettingsWrapper.vue`):
- Main container component that handles page layout, breadcrumbs, loading states
- Includes **SubmitChanges** component that automatically:
  - Tracks changes to settings data
  - Shows a floating action bar when changes are detected
  - Provides Save and Revert buttons
  - Handles form submissions and validation errors

**SettingsBox** (`vuejs-sitecp/src/components/SettingsBox/SettingsBox.vue`):
- Renders grouped settings using the `boxes` array structure
- Each box contains fields with specific types and components

### B. SettingsBox Standalone Pattern
Used for simpler settings or specialized forms.

#### Box Structure for SettingsBox
```javascript
setupBoxes() {
  this.boxes = [
    {
      key: "connect",
      group: "algolia",
      title: "Algolia connect",
      titleHelp: {
        label: "Algolia connect",
        parameters: [],
      },
      infoTitle: "Algolia connect",
      infoDescription: "Algolia connect",
      editMethod: "slide", // or "inline"
      lockEditMethod: false,
      hideActions: true,
      isVisible: true,
      hr: "hide",
      credentials: true,
      fields: [
        {
          key: "appId",
          type: "string",
          label: "Application ID",
          disabled: false,
          readonly: false,
          help: null,
          inputType: "string",
          credentials: true,
        },
        {
          key: "apiKey", 
          type: "string",
          label: "Admin API Key",
          disabled: false,
          readonly: false,
          help: null,
          inputType: "string",
          credentials: true,
        }
      ],
    }
  ];
}
```

#### Field Types for SettingsBox
- `string` - Text input
- `switch` - Toggle switch
- `number` - Number input
- `select` - Dropdown select
- `html` - Custom component via `markRaw(Component)`

### C. SubmitChanges Component Features
The SubmitChanges component (`vuejs-sitecp/src/components/SubmitChanges.vue`) provides:

1. **Automatic Change Detection**: Compares current form data with original values
2. **Floating Action Bar**: Shows when changes are detected with:
   - Save button (calls provided save function)
   - Revert button (restores original values)
   - Loading states during submission
3. **Validation Error Handling**: Displays errors returned from API
4. **Ignored Fields**: Can exclude specific fields from change detection

#### SubmitChanges Usage Pattern
```javascript
// In component template
<SettingsWrapper
  v-model:boxes="boxes"
  v-model:responseErrors="responseErrors"
  v-model:settings="settings"
  :action-submit="submitData"
  :loading="loading"
  :submit-loader="submitLoader"
  :translationLabels="translations"
  setting-key="general"
/>

// The SubmitChanges is automatically included in SettingsWrapper
// and will track changes to the 'settings' object
```

## 3. Error Handling

### Standard Error Response Pattern
**ALWAYS use `this.$errorResponse(error)`** for handling API errors in catch blocks:

```javascript
methods: {
  async saveSettings() {
    try {
      const response = await this.model.update(this.settings);
      // Handle success
    } catch (error) {
      this.$errorResponse(error);
    }
  }
}
```

### Custom Error Handling
For specific error handling needs:
```javascript
catch (error) {
  if (error.response?.status === 422) {
    this.validationErrors = error.response.data.errors;
  } else {
    this.$errorResponse(error);
  }
}
```

## 4. Translations

**ALWAYS use `this.$t('Translation Key')`** for all user-facing text. Never use static strings.

### Translation Pattern
```javascript
data() {
  return {
    translations: {
      title: this.$t('Theme Settings'),
      subtitle: this.$t('Customize your theme appearance'),
      save: this.$t('Save Changes'),
      revert: this.$t('Revert Changes'),
      // ... all other translations
    }
  };
}
```

### In Templates
```vue
<template>
  <h2>{{ translations.title }}</h2>
  <p>{{ translations.subtitle }}</p>
</template>
```

## 5. Component Reuse

### Core Form Components
Reuse existing form components instead of creating new ones:

- `TextInputComponent` - Text inputs
- `TextareaComponent` - Textarea fields  
- `ColorPicker` - Color selection
- `SelectWithAjax` - Ajax-powered selects
- `CheckboxComponent` - Checkboxes
- `RadioComponent` - Radio buttons
- `ActiveSwitch` - Toggle switches
- `InputNumberComponent` - Number inputs
- `ImageUploadModal` - Image uploads

### Component Usage
```vue
<script>
import ColorPicker from '@components/Form/ColorPicker.vue';
import SelectWithAjax from '@components/Form/SelectWithAjax.vue';

export default {
  components: {
    ColorPicker,
    SelectWithAjax,
  }
}
</script>
```

## 6. Date Formatting

Use moment.js for consistent date formatting:

```javascript
import moment from 'moment';

// In computed or methods
formatDate(date) {
  return moment(date).format('DD/MM/YYYY HH:mm');
}
```

## 7. Vue 3 Standards

### Composition API vs Options API
CloudCart primarily uses **Options API** for consistency across the admin panel. Use Composition API only when specifically needed for complex state management.

### Options API Structure
```javascript
export default {
  name: "ComponentName",
  components: {
    // Import components
  },
  props: {
    // Define props
  },
  data() {
    return {
      // Component state
      translations: {
        // All translations using this.$t()
      }
    };
  },
  computed: {
    // Computed properties
  },
  watch: {
    // Watchers
  },
  created() {
    // Lifecycle hooks
  },
  methods: {
    // Component methods
  }
}
```

## 8. Performance Guidelines

### Loading States
Always provide loading feedback:
```javascript
data() {
  return {
    loading: true,
    submitLoader: false
  };
}
```

### Template Loading
```vue
<template>
  <div v-if="loading" class="d-flex justify-content-center p-4">
    <div class="spinner-border" role="status"></div>
  </div>
  <div v-else>
    <!-- Content -->
  </div>
</template>
```

## 9. Settings Data Structure Patterns

### Theme Settings API Response Structure

#### Groups Endpoint Response (`/admin/api/theme-settings`)
```json
{
  "success": true,
  "data": {
    "groups": [
      {
        "id": "theme-info",
        "label": "theme_info", 
        "description": null,
        "settings_count": 0,
        "has_changes": false
      }
    ],
    "theme": {
      "id": 1,
      "name": "Vessel",
      "version": "1.0.0"
    },
    "meta": {
      "total_groups": 18,
      "has_schema": true,
      "last_updated": "2025-06-16T17:39:53+00:00"
    }
  }
}
```

#### Group Settings Endpoint Response (`/admin/api/theme-settings/{group}`)
```json
{
  "success": true,
  "data": {
    "group": {
      "id": "names-typography",
      "label": "Typography",
      "description": null
    },
    "settings": [
      {
        "id": "type_body_font",
        "type": "font_picker",
        "component": "FormSelect",
        "props": {
          "id": "type_body_font",
          "label": "Body",
          "placeholder": "",
          "info": null
        },
        "value": "red_hat_display_n4",
        "default": "work_sans_n4",
        "validation": [],
        "meta": {
          "label": "Body",
          "info": null,
          "required": false
        }
      }
    ],
    "meta": {
      "total_settings": 36,
      "has_changes": false
    }
  }
}
```

### Dynamic Group Icon Mapping
Since group IDs are dynamic per theme, use pattern matching for icons:

```javascript
// Icon mapping for common group patterns
const iconMap = {
  'logo': 'fas fa-image',
  'favicon': 'fas fa-image', 
  'colors': 'fas fa-palette',
  'typography': 'fas fa-font',
  'layout': 'fas fa-th-large',
  'animation': 'fas fa-magic',
  'badge': 'fas fa-award',
  'button': 'fas fa-hand-pointer',
  'cart': 'fas fa-shopping-cart',
  'drawer': 'fas fa-bars',
  'icon': 'fas fa-icons',
  'input': 'fas fa-edit',
  'popover': 'fas fa-comment',
  'price': 'fas fa-dollar-sign',
  'product': 'fas fa-th',
  'search': 'fas fa-search',
  'swatch': 'fas fa-swatchbook',
  'variant': 'fas fa-list-ul'
};

// Function to get icon based on group ID pattern
getGroupIcon(groupId) {
  for (const [pattern, icon] of Object.entries(iconMap)) {
    if (groupId.includes(pattern)) {
      return icon;
    }
  }
  return 'fas fa-cog'; // Default icon
}
```

This comprehensive approach ensures consistent, maintainable, and user-friendly Vue.js components throughout CloudCart's admin panel.
