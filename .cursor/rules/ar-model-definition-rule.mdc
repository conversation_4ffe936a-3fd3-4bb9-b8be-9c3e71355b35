---
description: The rule now provides developers with comprehensive guidance for working with CloudCart's sophisticated model architecture, including real-world examples and best practices found throughout the codebase. It covers everything from basic model structure to advanced patterns like multi-tenancy, caching, and event handling.
globs: 
alwaysApply: false
---
# CloudCart Model Definition Rule

## Overview

CloudCart follows a sophisticated model architecture with abstract base models, extensive trait usage, and comprehensive event handling. Models are organized by domain and follow strict patterns for multi-tenant operations, caching, and API integration.

## Model Architecture

### 1. Inheritance Structure

CloudCart uses abstract base models for consistent structure:

```php
<?php

declare(strict_types=1);

namespace App\Models\Product;

use App\Models\Base\AbstractProducts;
use App\Contracts\ApiModelContract;

class Product extends AbstractProducts implements ApiModelContract
{
    use Products;
    use GlobalImportRecord;
    use Searchable;
    use PlanUsage;
    use Redirect301;
    use Navigations;
    use Crudling;
    use ApiTrait;
    use UnitAttributes;
    use StoreFrontListing;
    use ModelDifferenceLog;
    use DescriptionImageToStorage;
    use ProductStatusByConditions;
    use ProductDelete;
    use CustomSoftDeletes;

    protected $connection = 'default';
    protected $table = 'products';
    
    // Model implementation
}
```

### 2. Abstract Base Models

Located in [app/Models/Base](mdc:app/Models/Base), these provide:

- **Database schema documentation** via PHPDoc
- **Foreign key relationships** documentation
- **Property definitions** with types
- **Query method signatures**
- **Consistent trait usage**

Example structure:
```php
/**
* Foreign Keys in table: products
* --------- products_ibfk_1 ---------
* Field: category_id
* Reference table: type__products_categories
* Reference field: id
* Actions: On delete - SET NULL; On update - NO ACTION
*/

/**
* @property integer id
* @property string name
* @property string url_handle
* @property \Carbon\Carbon date_added
* @property \Carbon\Carbon date_modified
*/
abstract class AbstractProducts extends Eloquent
{
    use ModelTrait;
    use DbTimezone;
    use Cacheable;
    use UrlHandle;
    use SeoFields;
    use AutoComplete;
    use ExternalMetaData;
    
    protected $connection = 'default';
    protected $fillable = ['name', 'description', 'price'];
    protected $guarded = ['id'];
}
```

## Essential Traits

### 1. Core Traits (Always Use)

```php
use App\Traits\Model as ModelTrait;
use App\Traits\DbTimezone;
use App\Traits\Crudling;
```

- **`ModelTrait`** - Core model functionality, bulk operations, polymorphic helpers
- **`DbTimezone`** - Timezone handling for multi-tenant operations  
- **`Crudling`** - CRUD operations, query scopes, and utilities

### 2. Specialized Traits

```php
// For models with URL handles
use App\Traits\UrlHandle;

// For models with SEO fields
use App\Traits\SeoFields;

// For cacheable models
use App\Traits\Cacheable;

// For API-enabled models
use App\Traits\Api as ApiTrait;

// For plan usage tracking
use App\Traits\PlanUsage;

// For soft deletes with custom logic
use App\Traits\CustomSoftDeletes;

// For search functionality
use App\Integration\Algolia\Traits\Searchable;

// For external metadata tracking
use App\Traits\ExternalMetaData;
```

## Model Structure Best Practices

### 1. Complete Model Template

```php
<?php

declare(strict_types=1);

namespace App\Models\YourDomain;

use App\Models\Base\AbstractYourModel;
use App\Contracts\ApiModelContract;
use App\Traits\Model as ModelTrait;
use App\Traits\DbTimezone;
use App\Traits\Crudling;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

/**
 * @property int $id
 * @property string $name
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read RelatedModel $related
 * @property-read Collection<ChildModel> $children
 * 
 * @method static Builder active()
 * @method static Builder byName(string $name)
 */
class YourModel extends AbstractYourModel implements ApiModelContract
{
    use ModelTrait;
    use DbTimezone;
    use Crudling;
    
    protected $connection = 'default';
    protected $table = 'your_models';
    
    protected $fillable = [
        'name', 'description', 'status'
    ];
    
    protected $guarded = [
        'id'
    ];
    
    protected $hidden = [
        'internal_field'
    ];
    
    protected $appends = [
        'computed_property'
    ];
    
    // Status column for activity management
    public $_status_column = 'active';
    
    // Enable publish date functionality
    protected $_has_publish_date = true;
    
    /**
     * The "booting" method of the model.
     */
    #[\Override]
    protected static function boot()
    {
        parent::boot();
        
        static::creating(function (self $model): void {
            // Logic before creating
        });
        
        static::created(function (self $model): void {
            // Logic after creating
            event(new YourModelCreated($model));
        });
        
        static::updating(function (self $model): void {
            // Logic before updating
        });
        
        static::updated(function (self $model): void {
            // Logic after updating
            event(new YourModelUpdated($model));
        });
        
        static::deleting(function (self $model): void {
            // Cleanup before deletion
            $model->children()->delete();
        });
        
        static::deleted(function (self $model): void {
            // Logic after deletion
            event(new YourModelDeleted($model));
        });
    }
    
    /**
     * Define attribute casting
     */
    protected function casts(): array
    {
        return [
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'metadata' => 'json',
            'is_active' => 'boolean',
        ];
    }
    
    // Relationships
    public function related(): BelongsTo
    {
        return $this->belongsTo(RelatedModel::class);
    }
    
    public function children(): HasMany
    {
        return $this->hasMany(ChildModel::class);
    }
    
    // Scopes
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('active', 'yes');
    }
    
    public function scopeByName(Builder $query, string $name): Builder
    {
        return $query->where('name', 'like', "%{$name}%");
    }
    
    // Accessors
    public function getComputedPropertyAttribute(): string
    {
        return $this->name . ' - ' . $this->id;
    }
    
    // Mutators
    public function setNameAttribute(string $value): void
    {
        $this->attributes['name'] = ucfirst(trim($value));
    }
}
```

### 2. Connection Management

```php
// Default site database
protected $connection = 'default';

// Router database for global data
protected $connection = 'router';

// MongoDB connections
protected $connection = 'mongodb-queue-local';
protected $connection = 'mongodb-analytics';
```

### 3. Table Naming Conventions

```php
// Standard tables
protected $table = 'products';
protected $table = 'product_categories';

// App-specific tables (prefixed with @app_)
protected $table = '@app_barsy_products';
protected $table = '@apps_memberships';

// Dynamic table names with brackets
protected $table = '[settings]';  // Resolved at runtime
protected $table = '[config]';
```

## Relationships Best Practices

### 1. Relationship Definitions

```php
/**
 * Get the category this product belongs to
 */
public function category(): BelongsTo
{
    return $this->belongsTo(Category::class, 'category_id', 'id');
}

/**
 * Get all variants for this product
 */
public function variants(): HasMany
{
    return $this->hasMany(Variant::class, 'product_id', 'id');
}

/**
 * Get categories this product belongs to (many-to-many)
 */
public function categories(): BelongsToMany
{
    return $this->belongsToMany(
        Category::class,
        'product_to_categories',
        'product_id',
        'category_id'
    )->withTimestamps();
}

/**
 * Polymorphic relationship
 */
public function commentable(): MorphTo
{
    return $this->morphTo();
}

/**
 * Custom multi-column relationship (CloudCart specific)
 */
public function customRelation(): BelongsToManyMultiColumns
{
    return $this->belongsToManyMultiColumns(
        RelatedModel::class,
        'foreign_key',
        ['other_key_1', 'other_key_2']
    );
}
```

### 2. Eager Loading

```php
// In model methods
public function scopeWithRelations(Builder $query): Builder
{
    return $query->with(['category', 'variants', 'images']);
}

// In controllers
$products = Product::with([
    'category:id,name',
    'variants' => function ($query) {
        $query->where('active', 'yes');
    },
    'images:id,product_id,url'
])->get();
```

## Query Scopes and Methods

### 1. Query Scopes

```php
/**
 * Scope for active records
 */
public function scopeActive(Builder $query): Builder
{
    return $query->where($this->_status_column ?? 'active', 'yes');
}

/**
 * Scope for published records
 */
public function scopePublished(Builder $query): Builder
{
    return $query->where('publish_date', '<=', now())
                 ->orWhereNull('publish_date');
}

/**
 * Scope for site-specific records (multi-tenant)
 */
public function scopeOwner(Builder $query, ?int $siteId = null): Builder
{
    return $query->where('site_id', $siteId ?: site('site_id'));
}

/**
 * Scope with parameters
 */
public function scopeByDateRange(Builder $query, Carbon $from, Carbon $to): Builder
{
    return $query->whereBetween('created_at', [$from, $to]);
}
```

### 2. Static Query Methods

```php
/**
 * Find by URL handle
 */
public static function findByHandle(string $handle): ?static
{
    return static::where('url_handle', $handle)->first();
}

/**
 * Get active records for current site
 */
public static function getActive(): Collection
{
    return static::active()->owner()->get();
}

/**
 * Bulk status change
 */
public static function changeActivity($ids, bool $active): int
{
    return static::whereIn('id', (array) $ids)
                 ->update(['active' => $active ? 'yes' : 'no']);
}
```

## Model Events and Observers

### 1. Model Events in boot() Method

```php
#[\Override]
protected static function boot()
{
    parent::boot();
    
    // Before creating
    static::creating(function (self $model): void {
        $model->site_id = site('site_id');
        $model->created_by = auth()->id();
    });
    
    // After creating
    static::created(function (self $model): void {
        // Fire domain events
        event(new ProductCreated($model));
        
        // Queue background jobs
        SiteQueue::executeQueueTask('process_new_product', [
            'product_id' => $model->id
        ]);
        
        // Update related data
        $model->category?->increment('products_count');
    });
    
    // Before updating
    static::updating(function (self $model): void {
        if ($model->isDirty('status')) {
            $model->status_changed_at = now();
        }
    });
    
    // After updating
    static::updated(function (self $model): void {
        event(new ProductUpdated($model));
        
        // Clear related caches
        CcCache::flushWithTags(['products', "product:{$model->id}"]);
    });
    
    // Before deleting
    static::deleting(function (self $model): void {
        // Cleanup related data
        $model->variants()->delete();
        $model->images()->delete();
        $model->categories()->detach();
    });
    
    // After deleting
    static::deleted(function (self $model): void {
        event(new ProductDeleted($model));
        
        // Update counters
        $model->category?->decrement('products_count');
    });
}
```

### 2. Event Subscribers

```php
// In EventServiceProvider
protected $subscribe = [
    ProductEventSubscriber::class,
];

// Subscriber class
class ProductEventSubscriber
{
    public function onProductCreated(ProductCreated $event): void
    {
        // Handle product creation
    }
    
    public function onProductUpdated(ProductUpdated $event): void
    {
        // Handle product updates
    }
    
    public function subscribe(Dispatcher $events): void
    {
        $events->listen(ProductCreated::class, $this->onProductCreated(...));
        $events->listen(ProductUpdated::class, $this->onProductUpdated(...));
        
        // Eloquent events
        $events->listen(
            'eloquent.deleting: ' . Product::class,
            $this->onDeleting(...)
        );
    }
}
```

## Attributes and Casting

### 1. Attribute Casting

```php
protected function casts(): array
{
    return [
        // Date casting
        'created_at' => 'datetime',
        'publish_date' => 'date',
        
        // JSON casting
        'metadata' => 'json',
        'settings' => 'array',
        
        // Boolean casting
        'is_active' => 'boolean',
        'featured' => 'boolean',
        
        // Numeric casting
        'price' => 'decimal:2',
        'weight' => 'float',
        'sort_order' => 'integer',
        
        // Enum casting (PHP 8.1+)
        'status' => ProductStatus::class,
    ];
}
```

### 2. Accessors and Mutators

```php
/**
 * Get formatted price
 */
public function getPriceFormattedAttribute(): string
{
    return Currency::format($this->price);
}

/**
 * Get full URL
 */
public function getLinkFrontAttribute(): string
{
    return Linker::product($this->url_handle);
}

/**
 * Set name with proper formatting
 */
public function setNameAttribute(string $value): void
{
    $this->attributes['name'] = trim($value);
    
    // Auto-generate URL handle if not set
    if (empty($this->url_handle)) {
        $this->attributes['url_handle'] = $this->generateHandle();
    }
}

/**
 * Set price with validation
 */
public function setPriceAttribute($value): void
{
    $this->attributes['price'] = max(0, (float) $value);
}
```

### 3. Computed Properties

```php
protected $appends = [
    'price_formatted',
    'link_front',
    'is_new',
    'category_path'
];

public function getIsNewAttribute(): bool
{
    return $this->created_at->isAfter(now()->subDays(30));
}

public function getCategoryPathAttribute(): string
{
    return $this->category?->getPath() ?? '';
}
```

## Serialization and API

### 1. Controlling Visibility

```php
// Hide sensitive fields
protected $hidden = [
    'internal_notes',
    'cost_price',
    'admin_only_field'
];

// Show only specific fields
protected $visible = [
    'id',
    'name',
    'price',
    'description'
];

// Always include computed properties
protected $appends = [
    'price_formatted',
    'image_url',
    'category_name'
];
```

### 2. Custom Serialization

```php
/**
 * Convert model to array for API
 */
public function toArray(): array
{
    $array = parent::toArray();
    
    // Add computed fields
    $array['category_name'] = $this->category?->name;
    $array['variants_count'] = $this->variants()->count();
    
    // Format dates
    $array['created_at_formatted'] = $this->created_at->format('Y-m-d H:i:s');
    
    return $array;
}

/**
 * API-specific formatting
 */
public function api(): array
{
    return [
        'id' => $this->id,
        'name' => $this->name,
        'price' => [
            'amount' => $this->price,
            'formatted' => $this->price_formatted,
            'currency' => site('currency_code')
        ],
        'urls' => [
            'view' => $this->link_front,
            'edit' => route('admin.products.edit', $this->id)
        ],
        'relationships' => [
            'category' => $this->category?->api(),
            'variants' => $this->variants->map->api()
        ]
    ];
}
```

## Multi-Tenant Patterns

### 1. Site Context

```php
#[\Override]
protected static function boot()
{
    parent::boot();
    
    // Auto-assign site_id on creation
    static::creating(function (self $model): void {
        if (!$model->site_id) {
            $model->site_id = site('site_id');
        }
    });
}

// Scope to current site
public function scopeOwner(Builder $query, ?int $siteId = null): Builder
{
    return $query->where('site_id', $siteId ?: site('site_id'));
}
```

### 2. Global vs Site-Specific Models

```php
// Site-specific model
class Product extends AbstractProducts
{
    protected static function boot()
    {
        parent::boot();
        
        static::creating(function (self $model): void {
            $model->site_id = site('site_id');
        });
    }
}

// Global model (no site_id)
class GlobalSetting extends AbstractGlobalSettings
{
    // No site_id assignment
}
```

## Performance Optimization

### 1. Query Optimization

```php
// Use select to limit columns
public function scopeListView(Builder $query): Builder
{
    return $query->select([
        'id', 'name', 'price', 'image_id', 'category_id'
    ]);
}

// Eager load relationships
public function scopeWithBasicRelations(Builder $query): Builder
{
    return $query->with([
        'category:id,name',
        'image:id,url',
        'variants' => function ($query) {
            $query->select('id', 'product_id', 'price', 'quantity');
        }
    ]);
}
```

### 2. Caching

```php
use App\Traits\Cacheable;

class Product extends AbstractProducts
{
    use Cacheable;
    
    // Cache tags for invalidation
    protected $cacheTags = ['products'];
    
    public function getCacheKey(): string
    {
        return "product:{$this->id}";
    }
    
    public function getCachedAttribute(string $key, callable $callback)
    {
        return Cache::tags($this->cacheTags)
                   ->remember(
                       "{$this->getCacheKey()}:{$key}",
                       3600,
                       $callback
                   );
    }
}
```

## Error Handling and Validation

### 1. Model Validation

```php
#[\Override]
protected static function boot()
{
    parent::boot();
    
    static::saving(function (self $model): void {
        // Validate before saving
        if (empty($model->name)) {
            throw new ValidationException('Name is required');
        }
        
        if ($model->price < 0) {
            throw new ValidationException('Price cannot be negative');
        }
    });
}
```

### 2. Exception Handling

```php
public function delete(): ?bool
{
    try {
        // Check if can be deleted
        if ($this->orders()->exists()) {
            throw new Error('Cannot delete product with existing orders');
        }
        
        return parent::delete();
        
    } catch (Throwable $e) {
        Log::error('Failed to delete product', [
            'product_id' => $this->id,
            'error' => $e->getMessage()
        ]);
        
        throw $e;
    }
}
```

## Testing Patterns

### 1. Model Factories

```php
// In database/factories/ProductFactory.php
class ProductFactory extends Factory
{
    protected $model = Product::class;
    
    public function definition(): array
    {
        return [
            'name' => $this->faker->productName(),
            'description' => $this->faker->paragraph(),
            'price' => $this->faker->randomFloat(2, 10, 1000),
            'active' => 'yes',
            'site_id' => 1,
        ];
    }
    
    public function inactive(): static
    {
        return $this->state(['active' => 'no']);
    }
}
```

### 2. Model Testing

```php
class ProductTest extends TestCase
{
    public function test_product_creation(): void
    {
        $product = Product::factory()->create([
            'name' => 'Test Product'
        ]);
        
        $this->assertDatabaseHas('products', [
            'name' => 'Test Product',
            'site_id' => site('site_id')
        ]);
    }
    
    public function test_product_relationships(): void
    {
        $category = Category::factory()->create();
        $product = Product::factory()->create([
            'category_id' => $category->id
        ]);
        
        $this->assertEquals($category->id, $product->category->id);
    }
}
```

This comprehensive model definition rule provides developers with all the patterns and best practices needed to work effectively with CloudCart's model architecture.