---
description: This rule provides practical guidelines and best practices for developing, organizing, and maintaining Liquid-based templates, sections, snippets, and theme assets on the CloudCart platform. It is intended for theme and template authors working within the CloudCart Liquid architecture.
globs: 
alwaysApply: false
---
# CloudCart Liquid Template Development Rule

## Purpose
This rule provides practical guidelines and best practices for developing, organizing, and maintaining Liquid-based templates, sections, snippets, and theme assets on the CloudCart platform. It is intended for theme and template authors working within the CloudCart Liquid architecture.

**For engine-level architecture and extension, see:** [ar-liquid-architecture-rule.mdc](mdc:ar-liquid-architecture-rule.mdc)

---

## 0. CSS Styling Standard: Tailwind CSS v4.1.6

All new Liquid templates, sections, and snippets **must use [Tailwind CSS v4.1.6](mdc:https:/tailwindcss.com/docs/installation)** for styling. This ensures a consistent, utility-first approach to design and rapid development.

- **Importing Tailwind**: Ensure your theme's main CSS file (e.g., `assets/app-tailwind-output.min.css`) is built with Tailwind v4.1.6 and included in `layout/theme.liquid`:
  ```liquid
  <link href="{{ 'app-tailwind-output.min.css' | asset_url }}" rel="stylesheet">
  ```
- **Generating CSS Output**: To compile your Tailwind CSS, create a `package.json` file in your theme's root directory.

  **Example `package.json`:**
  ```json
  {
    "name": "your-theme-name",
    "version": "1.0.0",
    "scripts": {
      "build": "npx tailwindcss -i assets/app-tailwind.css -o assets/app-tailwind-output.min.css --minify",
      "watch": "npx tailwindcss -i assets/app-tailwind.css -o assets/app-tailwind-output.css --watch"
    },
    "dependencies": {
      "@tailwindcss/cli": "^4.1.6",
      "@tailwindcss/postcss": "^4.1.6",
      "postcss": "^8.5.3",
      "tailwindcss": "^4.1.6"
    }
  }
  ```
  - **For development**: Run `npm install` and then `npm run watch`. This command watches for changes in `assets/app-tailwind.css` and automatically generates an unminified `app-tailwind-output.css`.
  - **For production**: Run `npm run build`. This generates a minified `app-tailwind-output.min.css` file, which is what you should link in `theme.liquid`.

- **Usage**: Use Tailwind utility classes directly in your HTML/Liquid markup:
  ```liquid
  <div class="flex items-center justify-between p-4 bg-gray-100 rounded-lg">
    ...
  </div>
  ```
- **Custom Styles**: If you need custom CSS, add it to your Tailwind config or use `@apply` in your CSS files. Avoid inline `<style>` blocks in templates.
- **Responsiveness**: Use Tailwind's responsive utilities (e.g., `md:`, `lg:`) for adaptive layouts.
- **Dark Mode**: Use Tailwind's dark mode classes if your theme supports it.
- **Reference**: [Tailwind CSS v4.1.6 Documentation](mdc:https:/tailwindcss.com/docs/installation)

---

## 0.5. Client-Side Interactivity: Alpine.js

For lightweight client-side interactivity, **Alpine.js is the recommended library**. It allows you to add behavior directly to your HTML markup, keeping your templates self-contained and easy to manage.

- **Installation**: Include Alpine.js via CDN in the `<head>` of your `layout/theme.liquid` file, before your main JavaScript file.
  ```liquid
  <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
  ```

- **Usage Example (Dropdown Menu)**: Here is how you can create a simple dropdown menu, like the user account menu in `hero.liquid`.

  ```liquid
  <div x-data="{ open: false }">
    <button @click="open = !open">Toggle Menu</button>
  
    <ul x-show="open" @click.away="open = false">
      <li><a href="/account/orders">My Orders</a></li>
      <li><a href="/account">My Details</a></li>
    </ul>
  </div>
  ```
  - `x-data="{ open: false }"`: Initializes a component scope with an `open` property set to `false`.
  - `@click="open = !open"`: Toggles the `open` property when the button is clicked.
  - `x-show="open"`: Shows or hides the `<ul>` element based on the value of `open`.
  - `@click.away="open = false"`: Closes the dropdown when a click is registered outside of it.

- **Key Directives**:
  - `x-data`: Declares a new component scope.
  - `x-init`: Runs an expression when a component is initialized.
  - `x-show`: Toggles the visibility of an element.
  - `x-on` (or `@`): Attaches an event listener.
  - `x-bind` (or `:`): Sets an attribute value from a JavaScript expression.
  - `x-text` / `x-html`: Sets the text or inner HTML of an element.
  - `x-for`: Creates DOM elements by iterating over a list.

- **Reference**: [Alpine.js Documentation](mdc:https:/alpinejs.dev/docs/start-here)

---

## 1. Theme Structure & File Organization

Follow Shopify-inspired conventions for theme structure:

```
resources/themes/{theme_name}/
├── layout/         # Main layout files (e.g., theme.liquid)
├── templates/      # Page templates (e.g., product.liquid, index.json)
├── sections/       # Modular, reusable page sections (e.g., hero.liquid)
├── snippets/       # Small, reusable code fragments (e.g., price.liquid)
├── assets/         # CSS, JS, images, fonts, etc.
├── config/         # Theme settings (settings_schema.json, settings_data.json)
└── locales/        # Translations (en.default.json, etc.)
```

- **layout/theme.liquid**: The root HTML structure for all pages.
- **templates/**: Each file defines a page type (e.g., product, collection, page, cart). Use `.liquid` or `.json` as appropriate.
- **sections/**: Each file is a modular, independently configurable block. Sections can be included in templates or referenced in JSON templates.
- **snippets/**: For small, reusable code (e.g., price display, icons).
- **assets/**: Store all static resources here.
- **config/**: Define theme settings and schema for the theme editor.
- **locales/**: Store translation files for multi-language support.

---

## 2. Template & Section Best Practices

### 2.1 Naming Conventions
- Use `kebab-case` for filenames (e.g., `main-product.liquid`, `cart-sidebar.liquid`).
- Use descriptive names for sections and snippets.
- Keep section and snippet names unique within a theme.

### 2.2 Section Structure
- Each section should start with the main HTML/Liquid code.
- End each section with a `{% schema %} ... {% endschema %}` block describing settings, blocks, and presets.
- Use `{% presets %}` for default section configurations.

**Example:**
```liquid
<!-- sections/hero.liquid -->
<div class="hero">{{ section.settings.headline }}</div>

{% schema %}
{
  "name": "Hero Section",
  "settings": [
    { "type": "text", "id": "headline", "label": "Headline" }
  ],
  "presets": [
    { "name": "Default Hero" }
  ]
}
{% endschema %}
```

### 2.3 Snippet Usage
- Use `{% render 'snippet-name', param: value %}` to include snippets.
- Snippets should be stateless and reusable.
- Document expected parameters at the top of the file using `{% comment %}`.

### 2.4 JSON Templates
- Use `.json` templates for dynamic, section-driven pages (e.g., `index.json`).
- Define the order and settings for sections in the JSON structure.
- Reference section types by their filename (without extension).

---

## 3. Theme Settings & Schema

- Define theme-wide settings in `config/settings_schema.json`.
- Store current values in `config/settings_data.json`.
- Use settings in templates via the `settings` object (e.g., `{{ settings.logo }}`).
- Use translation keys in schema labels for multi-language support.

---

## 4. Filters, Tags, and Drops

- Use only registered filters and tags (see [config/liquid.php](mdc:config/liquid.php)).
- For custom logic, prefer snippets or sections over inline code.
- Use Drop classes for complex objects (e.g., `product`, `cart`).
- Reference the [LIQUID_FILTERS.md](mdc:app/LiquidEngine/LIQUID_FILTERS.md) for available filters and usage.

---

## 5. Best Practices

- **Keep logic minimal in templates**: Move business logic to controllers or Drops.
- **Use global objects**: `shop`, `cart`, `customer`, `settings` are available in context.
- **Document your code**: Use `{% comment %}` for usage and parameter documentation.
- **Test with different data**: Ensure sections and templates work with various settings and edge cases.
- **Fallbacks**: Use the `_global/global-theme` for fallback templates and sections.
- **Performance**: Minimize loops and expensive operations in templates. Use caching where possible.
- **Accessibility**: Use semantic HTML and ARIA attributes where appropriate.

---

## 6. Troubleshooting

- **Template not found**: Ensure the file exists in the correct directory and has the right extension.
- **Tag/filter not working**: Check registration in `config/liquid.php` and class existence.
- **Settings not appearing**: Validate schema in `settings_schema.json` and check for typos in IDs.
- **Multi-language**: Ensure all labels and help texts use translation keys and that locale files are up to date.

---

## 7. References
- [ar-liquid-architecture-rule.mdc](mdc:ar-liquid-architecture-rule.mdc)
- [LIQUID_FILTERS.md](mdc:app/LiquidEngine/LIQUID_FILTERS.md)
- [config/liquid.php](mdc:config/liquid.php)
- [Shopify Liquid Documentation](mdc:https:/shopify.dev/docs/themes/liquid/reference)
- [Tailwind CSS v4.1.6 Documentation](mdc:https:/tailwindcss.com/docs/installation)

---

## 8. Schema Configuration Types and Color Schemes

### 8.1 Understanding `color_scheme` in Theme Configuration

In theme development, `color_scheme` is a **special identifier** used in **JSON section configuration** (in `settings_schema.json` or in `sections/*.liquid`) to allow users to select predefined color schemes.

#### 🔹 What is `color_scheme`?

This is a **reference** to defined color schemes in the theme. It's commonly used in sections like:

```json
{
  "type": "color_scheme",
  "id": "color_scheme",
  "label": "Color scheme"
}
```

#### 🔹 Where are color schemes defined?

They are defined in `settings_schema.json`, usually in an array with `"name": "color_schemes"`:

```json
{
  "name": "color_schemes",
  "settings": [
    {
      "name": "Light",
      "settings": {
        "background": "#ffffff",
        "text": "#000000"
      }
    },
    {
      "name": "Dark",
      "settings": {
        "background": "#000000",
        "text": "#ffffff"
      }
    }
  ]
}
```

#### 🔹 How to use in Liquid?

The platform automatically provides the `section.settings.color_scheme` object, which you can use, for example:

```liquid
<div class="section" style="background: {{ settings.color_scheme.settings.background }}; color: {{ settings.color_scheme.settings.text }};">
  {{ section.settings.heading }}
</div>
```

Or more concisely through `color_scheme_class`:

```liquid
<div class="section {{ section.settings.color_scheme | color_scheme_class }}">
  ...
</div>
```

#### ✅ Summary

| Element          | Description                                                                                    |
| ---------------- | ------------------------------------------------------------------------------------------- |
| `color_scheme`   | UI dropdown menu that allows selection of a color scheme.                                     |
| Definitions      | In `settings_schema.json`, in `"color_schemes"` array.                                        |
| Usage in code    | Through `section.settings.color_scheme`, `color_scheme_class`, or direct access to colors.   |

### 8.2 Complete Table of Valid `type` Values

Here's a **complete table with all valid `type` values**, which files they can be used in, and examples for each type:

| `type`             | Allowed in `settings_schema.json` | Allowed in `sections/*.liquid` | Example                                                                         |
| ------------------ | :--------------------------------: | :-----------------------------: | ------------------------------------------------------------------------------ |
| `text`             |                  ✅                 |                ✅                | `"type": "text", "id": "heading", "label": "Heading"`                          |
| `textarea`         |                  ✅                 |                ✅                | `"type": "textarea", "id": "desc", "label": "Description"`                     |
| `richtext`         |                  ✅                 |                ✅                | `"type": "richtext", "id": "content", "label": "Rich Content"`                 |
| `html`             |                  ✅                 |                ✅                | `"type": "html", "id": "custom_html", "label": "HTML"`                         |
| `url`              |                  ✅                 |                ✅                | `"type": "url", "id": "link", "label": "URL"`                                  |
| `checkbox`         |                  ✅                 |                ✅                | `"type": "checkbox", "id": "show_title", "label": "Show title"`                |
| `radio`            |                  ✅                 |                ✅                | `"type": "radio", "id": "style", "label": "Style", "options": [...]`           |
| `select`           |                  ✅                 |                ✅                | `"type": "select", "id": "layout", "options": [...]`                           |
| `range`            |                  ✅                 |                ✅                | `"type": "range", "id": "opacity", "min": 0, "max": 100, "step": 10`           |
| `number`           |                  ✅                 |                ✅                | `"type": "number", "id": "count", "label": "Items"`                            |
| `color`            |                  ✅                 |                ✅                | `"type": "color", "id": "text_color", "label": "Text color"`                   |
| `color_background` |                  ✅                 |                ✅                | `"type": "color_background", "id": "bg_color", "label": "Background"`          |
| `font_picker`      |                  ✅                 |                ✅                | `"type": "font_picker", "id": "font", "label": "Font"`                         |
| `image_picker`     |                  ✅                 |                ✅                | `"type": "image_picker", "id": "image", "label": "Image"`                      |
| `video_url`        |                  ✅                 |                ✅                | `"type": "video_url", "id": "video", "label": "YouTube/Vimeo video"`           |
| `color_scheme`     |                  ✅                 |                ✅                | `"type": "color_scheme", "id": "color_scheme", "label": "Color scheme"`        |
| `liquid`           |                  ✅                 |                ✅                | `"type": "liquid", "id": "liquid_code", "label": "Custom code"`                |
| `product`          |                  ❌                 |                ✅                | `"type": "product", "id": "featured_product", "label": "Product"`              |
| `collection`       |                  ❌                 |                ✅                | `"type": "collection", "id": "featured_collection", "label": "Collection"`     |
| `blog`             |                  ❌                 |                ✅                | `"type": "blog", "id": "blog", "label": "Blog"`                                |
| `article`          |                  ❌                 |                ✅                | `"type": "article", "id": "article", "label": "Blog post"`                     |
| `page`             |                  ❌                 |                ✅                | `"type": "page", "id": "page", "label": "Page"`                                |
| `link_list`        |                  ❌                 |                ✅                | `"type": "link_list", "id": "menu", "label": "Menu"`                           |
| `header`           |                  ✅                 |                ✅                | `"type": "header", "content": "Section Settings"`                              |
| `paragraph`        |                  ✅                 |                ✅                | `"type": "paragraph", "content": "This section controls the homepage banner."` |

#### 📝 Notes

* In `settings_schema.json` **cannot use** `product`, `collection`, `article`, `blog`, `page`, `link_list`.
* For these types, the platform doesn't provide access to resources in the global context, only in sections and blocks.

### 8.3 Font Picker Values and Sources

The `font_picker` type is special - it draws its values **directly from the platform's font library**, not from your theme. Here's how it works:

#### 🎨 `font_picker`: Description

The `font_picker` type allows users to select a font from the **official platform font library**, based on Google Fonts plus some additional fonts.

#### 🔸 Example:

```json
{
  "type": "font_picker",
  "id": "heading_font",
  "label": "Heading font",
  "default": "work_sans_n4"
}
```

#### 🧠 Values and Format

The platform uses **font identifiers** based on:

```
<font_name>_<style><weight>
```

#### 🔹 Example Values:

| Display Text      | Value (`value`)    |
| ----------------- | ------------------ |
| Work Sans Regular | `work_sans_n4`     |
| Work Sans Bold    | `work_sans_n7`     |
| Roboto Italic     | `roboto_i4`        |
| Roboto BoldItalic | `roboto_i7`        |

#### 🔸 Legend:

* `n4` → normal, weight 400
* `n7` → normal, weight 700
* `i4` → italic, weight 400
* `i7` → italic, weight 700

#### 🔧 Usage in CSS

The platform provides the `font_face` function in Liquid, so you can use the selected font easily:

```liquid
{{ settings.heading_font | font_face }}
<style>
  h1 {
    font-family: {{ settings.heading_font.family }}, sans-serif;
    font-style: {{ settings.heading_font.style }};
    font-weight: {{ settings.heading_font.weight }};
  }
</style>
```

#### ⚠️ Notes

* **You cannot add custom fonts** to `font_picker`.
* For custom fonts, you must load them manually (via CSS or `@font-face`) and use `select` instead of `font_picker`.

---

## 9. Example Directory Layout

```
resources/themes/my-theme/
├── layout/
│   └── theme.liquid
├── templates/
│   ├── index.json
│   ├── product.liquid
│   └── page.liquid
├── sections/
│   ├── hero.liquid
│   └── featured-products.liquid
├── snippets/
│   ├── price.liquid
│   └── icon-cart.liquid
├── assets/
│   ├── style.css
│   └── main.js
├── config/
│   ├── settings_schema.json
│   └── settings_data.json
└── locales/
    └── en.default.json
```
