---
description: This rule provides comprehensive guidelines for creating and structuring Vue3 modules within CloudCart's sitecp (`vuejs-sitecp/src/CcModules/`). Vue modules are automatically discovered and initialized through their `setup.js` files, providing a modular architecture for admin panel features.
globs: 
alwaysApply: false
---
# Vue Module Development Rule for CloudCart SiteCP

## Description
This rule provides comprehensive guidelines for creating and structuring Vue3 modules within CloudCart's sitecp (`vuejs-sitecp/src/CcModules/`). Vue modules are automatically discovered and initialized through their `setup.js` files, providing a modular architecture for admin panel features.

## 1. Module Discovery and Initialization

### 1.1 Automatic Module Scanning
- Vue scans the `vuejs-sitecp/src/CcModules/` directory for `setup.js` files
- Each module directory must contain a `setup.js` file to be recognized
- Modules are automatically registered and initialized during application startup

### 1.2 Module Naming Convention
- Use **PascalCase** for module directory names
- Examples: `CcAnalytics`, `Gdpr`, `ThemeSettings`, `PaymentProviders`
- Avoid special characters and spaces in module names

## 2. Required Module Structure

### 2.1 Essential Files and Directories
```
ModuleName/
├── setup.js                    # REQUIRED - Module entry point
├── router/
│   └── index.js               # Route definitions
├── components/
│   ├── Index.vue              # Main module component
│   ├── Tabs/                  # Tab components
│   ├── Modals/                # Modal components
│   ├── Helpers/               # Helper components
│   └── TableColumns/          # Table-specific components
├── js/                        # JavaScript utilities and composables
├── scss/                      # Module-specific styles
├── plugins/                   # Vue plugins (optional)
└── config.json               # Module configuration (optional)
```

### 2.2 setup.js - Module Entry Point
**REQUIRED**: Every module must have a `setup.js` file that exports module configuration.

**Basic structure:**
```javascript
import {routes} from './router';

export {
    routes,
}
```

**Advanced structure with plugins:**
```javascript
import {routes} from './router';
import {plugins} from './plugins';

export {
    routes,
    plugins,
}
```

## 3. Router Configuration

### 3.1 Router Structure
- Create `router/index.js` for route definitions
- Export `routes` array that will be merged into the main router
- Use dynamic imports for lazy loading components

### 3.2 Route Naming Convention
- Use dot notation for nested routes
- Start with module context (e.g., `apps.`, `admin.`)
- Examples: `apps.gdpr`, `apps.gdpr.settings`, `admin.theme.settings`

### 3.3 Router Example
```javascript
let routes = [
  {
    name: "apps.modulename",
    path: "/admin/apps/modulename",
    component: () => import("./../components/Index"),
    props: {
      appKey: 'modulename',
    },
    children: [
      {
        name: "apps.modulename.settings",
        path: "settings",
        component: () => import("./../components/Tabs/Settings"),
      },
      {
        name: "apps.modulename.overview",
        path: "overview",
        component: () => import("../../Erp/Core/components/Tabs/ErpOverview"),
      }
    ]
  },
];

export { routes };
```

## 4. Component Organization

### 4.1 Main Component (Index.vue)
- Create `components/Index.vue` as the main module component
- Should handle module layout, navigation, and tab management
- Include module-specific logic and state management

### 4.2 Component Directory Structure
- **`Tabs/`** - Components for different module sections/tabs
- **`Modals/`** - Modal dialog components
- **`Helpers/`** - Reusable helper components
- **`TableColumns/`** - Table-specific components and column definitions

### 4.3 Component Naming
- Use **PascalCase** for component files
- Be descriptive: `ConsentSections.vue`, `AddressForm.vue`, `Requests.vue`
- Group related components in subdirectories

## 5. JavaScript Organization

### 5.1 JS Directory Structure
```
js/
├── composables/              # Vue 3 composables
├── utils/                    # Utility functions
├── api/                      # API service functions
├── constants/                # Module constants
└── stores/                   # Pinia stores (if needed)
```

### 5.2 Composables Pattern
- Create reusable logic as Vue 3 composables
- Name with `use` prefix: `useModuleSettings.js`, `useApiClient.js`
- Export composables that can be used across components

## 6. Plugins System (Optional)

### 6.1 Plugin Structure
If your module needs to extend global functionality:

```
plugins/
├── index.js                  # Plugin registration
├── PluginName/
│   ├── index.js
│   └── PluginComponent.vue
```

### 6.2 Plugin Registration
```javascript
// plugins/index.js
import {PluginOne} from './PluginOne'
import {PluginTwo} from './PluginTwo'

export const plugins = [
    PluginOne,
    PluginTwo,
]
```

### 6.3 Plugin Export in setup.js
```javascript
import {routes} from './router';
import {plugins} from './plugins';

export {
    routes,
    plugins,
}
```

## 7. Translations System

### 7.1 Component-Level Translations
- **Translations are handled at the component level** using a `translations` key in the component's `data()` section
- **CloudCart's translation tool** reads this data and handles automatic translation
- **No separate translation files needed** - translations are embedded in components

### 7.2 Translation Pattern in Components
```javascript
// In Vue component
export default {
    data() {
        return {
            translations: {
                "Module Settings": this.$t("Module Settings"),
                "Save Changes": this.$t("Save Changes"),
                "Reset to Default": this.$t("Reset to Default"),
                "Loading...": this.$t("Loading..."),
                "Error occurred": this.$t("Error occurred"),
            },
            // ... other data
        };
    },
    // ... rest of component
}
```

### 7.3 Translation Best Practices
- **Define all translatable strings** in the component's `translations` data property
- **Use descriptive keys** that clearly indicate the text purpose
- **Group related translations** logically within the translations object
- **CloudCart's translation tool** automatically processes these translations

## 8. Configuration (Optional)

### 8.1 Module Configuration
Create `config.json` for module-specific configuration:

```json
{
  "export": {
    "limit": 150000
  },
  "api": {
    "baseUrl": "/api/module-name",
    "timeout": 30000
  },
  "features": {
    "advancedMode": true,
    "debugMode": false
  }
}
```

## 9. Styling

### 9.1 SCSS Organization
```
scss/
├── _variables.scss           # Module variables
├── _mixins.scss             # Module mixins
├── _components.scss         # Component styles
└── main.scss                # Main stylesheet
```

### 9.2 Styling Best Practices
- Use CSS modules or scoped styles in components
- Follow CloudCart's design system
- Leverage existing utility classes
- Keep module styles isolated

## 10. Development Best Practices

### 10.1 Build Commands
**Important**: Use the correct build commands for development and production:

- **Development Build**: `npm run build-dev` - Faster builds for development with source maps
- **Production Build**: `npm run build` - Optimized builds for production deployment
- **Watch Mode**: `npm run watch` - Continuous building during development

**Always use `npm run build-dev` during development** for faster iteration and debugging capabilities.

### 10.2 Module Isolation
- Keep modules self-contained
- Avoid dependencies on other modules
- Use shared utilities from `src/components/` and `src/js/`

### 10.3 State Management
- Use Vue 3 Composition API
- Create composables for shared state
- Use Pinia stores for complex state management

### 10.4 API Integration
- Create dedicated API service files in `js/api/`
- Use consistent error handling
- Implement proper loading states

### 10.5 Performance Considerations
- Use lazy loading for routes and components
- Implement proper caching strategies
- Optimize bundle size with tree shaking

## 11. Testing

### 11.1 Testing Structure
```
__tests__/
├── components/
├── composables/
└── utils/
```

### 11.2 Testing Guidelines
- Write unit tests for composables and utilities
- Test component behavior and user interactions
- Mock API calls and external dependencies

## 12. Module Examples

### 12.1 Simple Module (Routes Only)
```javascript
// setup.js
import {routes} from './router';

export {
    routes,
}
```

### 12.2 Complex Module (With Plugins)
```javascript
// setup.js
import {routes} from './router';
import {plugins} from './plugins';

export {
    routes,
    plugins,
}
```

## 13. Integration Guidelines

### 13.1 Using Existing Components
- Leverage form components from `src/components/Form/`
- Use shared utilities and mixins
- Follow established patterns from existing modules

### 13.2 API Integration
- Use CloudCart's established API patterns
- Follow namespace conventions
- Implement proper error handling and validation

## 14. Common Patterns

### 14.1 Module Navigation
- Use tabbed interface for complex modules
- Implement breadcrumb navigation
- Provide clear entry points

### 14.2 Data Management
- Implement CRUD operations consistently
- Use reactive data patterns
- Provide real-time updates where appropriate

### 14.3 User Experience
- Implement loading states
- Provide clear feedback messages
- Follow CloudCart's UX patterns

## 15. Troubleshooting

### 15.1 Module Not Loading
- Ensure `setup.js` exists and exports properly
- Check for JavaScript errors in console
- Verify route paths and component imports

### 15.2 Route Conflicts
- Use unique route names
- Check for path conflicts with existing routes
- Verify proper route hierarchy

## 16. Migration and Updates

### 16.1 Updating Existing Modules
- Maintain backward compatibility
- Update dependencies gradually
- Test thoroughly after changes

### 16.2 Module Deprecation
- Follow deprecation guidelines
- Provide migration paths
- Document breaking changes

This rule ensures consistent, maintainable, and scalable Vue module development within CloudCart's sitecp architecture.
