---
description:
globs:
alwaysApply: false
---
# CloudCart Bug Fixing and Refactoring Guide

## Description
Comprehensive guide for debugging, bug fixing, and refactoring code in CloudCart's multi-tenant e-commerce platform. Covers Laravel-specific debugging techniques, CloudCart's error handling patterns, and systematic refactoring approaches.

---

## 1. Debugging Techniques

### 1.1 CloudCart Debug Tools

#### Debug Mode Configuration
```php
// Enable debug mode in .env
APP_DEBUG=true

// Check debug status in code
if (config('app.debug')) {
    // Debug-specific logic
}

// Use debug cookie for specific debugging
// Set cookie: edebug=1 to trigger dd() in exception handler
if ($request->cookie('edebug')) {
    dd($exception);
}
```

#### Built-in Debug Helpers
```php
// CloudCart's CliDebug helper for console commands
use App\Helper\CliDebug;

$debug = new CliDebug();
$debug->info('Processing started');
$debug->warn('Potential issue detected');
$debug->error('Critical error occurred');

// Get all debug messages
$messages = $debug->getOutputMessages();
```

#### Database Query Debugging
```php
// Enable query logging
DB::enableQueryLog();

// Your database operations here
$products = Product::with('variants')->get();

// Get executed queries
$queries = DB::getQueryLog();
foreach ($queries as $query) {
    dump($query['query'], $query['bindings'], $query['time']);
}
```

### 1.2 Performance Debugging

#### Memory Usage Tracking
```php
// Track memory usage
$startMemory = memory_get_usage(true);
$peakMemory = memory_get_peak_usage(true);

// Your code here

$endMemory = memory_get_usage(true);
$memoryUsed = $endMemory - $startMemory;

$this->info("Memory used: " . Format::bytes($memoryUsed));
$this->info("Peak memory: " . Format::bytes($peakMemory));
```

#### Execution Time Profiling
```php
// Time critical operations
$start = microtime(true);

// Your operation
ProductTemp::createTempTableAndPopulate($customerGroupId);

$executionTime = microtime(true) - $start;
$this->info(sprintf('Operation completed in %.2f seconds', $executionTime));
```

#### Query Performance Analysis
```php
// Use EXPLAIN for query optimization
$query = Product::with('variants')
    ->where('active', 'yes')
    ->where('category_id', $categoryId);

// Get the SQL and analyze
$sql = $query->toSql();
$bindings = $query->getBindings();

// Run EXPLAIN
$explain = DB::select("EXPLAIN $sql", $bindings);
dump($explain);
```

### 1.3 Multi-Tenant Debugging

#### Site Context Debugging
```php
// Debug current site context
$currentSite = site();
$this->info("Current site: {$currentSite->site_id}");
$this->info("Database: {$currentSite->database}");
$this->info("Namespace: " . app_namespace());

// Debug database connections
$connections = DB::getConnections();
foreach ($connections as $name => $connection) {
    $this->info("Connection: $name -> " . $connection->getDatabaseName());
}
```

#### Namespace-Specific Debugging
```php
// Debug middleware and providers by namespace
$namespace = app_namespace();
$middlewares = config("middlewareByNamespace.$namespace", []);
$providers = config("providersByNamespace.$namespace", []);

dump("Namespace: $namespace", $middlewares, $providers);
```

---

## 2. Error Handling and Exception Management

### 2.1 CloudCart Exception Hierarchy

#### Custom Exception Types
```php
// Use CloudCart's custom exceptions
use App\Exceptions\Error;
use App\Exceptions\Fault;
use App\Exceptions\Site\HttpNotFound;
use App\Exceptions\Site\SiteMaintenance;

// Business logic errors
throw new Error('Invalid product configuration', 'product_id', 422);

// System faults
throw new Fault('Database connection failed');

// Site-specific errors
throw new HttpNotFound('Product not found');
```

#### Exception Logging Patterns
```php
// Use CloudCart's exception logging
use App\Models\Router\Exceptions;

try {
    // Risky operation
    $result = $externalApi->call();
} catch (Throwable $e) {
    // Log to CloudCart's exception system
    Exceptions::createFromThrowable($e, 'External API', $e->getMessage(), [
        'context' => 'product_sync',
        'product_id' => $productId,
        'api_endpoint' => $endpoint
    ]);
    
    // Re-throw or handle gracefully
    throw new Error('Product sync failed', 'sync_error');
}
```

### 2.2 Error Response Patterns

#### API Error Responses
```php
// For API2 namespace (JSON API)
use CloudCreativity\LaravelJsonApi\Document\Error as ApiError;
use Neomerx\JsonApi\Exceptions\JsonApiException;

$error = ApiError::create([
    'status' => 422,
    'title' => 'Validation Error',
    'detail' => 'Product name is required',
    'source' => ['pointer' => '/data/attributes/name']
]);

throw new JsonApiException($error);
```

#### AJAX Error Responses
```php
// For AJAX requests in sitecp namespace
if ($request->ajax()) {
    return response([
        'status' => 'error',
        'field' => 'product_name',
        'msg' => 'Product name is required'
    ], 422);
}
```

#### Validation Error Handling
```php
// Use Laravel's validation with CloudCart patterns
try {
    $validated = $request->validate([
        'name' => 'required|string|max:255',
        'price' => 'required|numeric|min:0'
    ]);
} catch (ValidationException $e) {
    if ($request->ajax()) {
        return response([
            'status' => 'error',
            'errors' => $e->errors(),
            'message' => 'Validation failed'
        ], 422);
    }
    
    throw $e; // Let the handler deal with it
}
```

### 2.3 Graceful Degradation

#### Service Availability Checks
```php
// Check external service availability
try {
    $response = $httpClient->get($endpoint, ['timeout' => 5]);
    return $response->json();
} catch (RequestException $e) {
    // Log the issue
    Exceptions::createFromThrowable($e, 'External Service');
    
    // Return cached data or default
    return CcCache::get("fallback_data_{$key}", [], ['external_services']);
}
```

#### Database Fallback Patterns
```php
// Fallback to alternative data source
try {
    return $this->getPrimaryData();
} catch (QueryException $e) {
    Exceptions::createFromThrowable($e, 'Primary DB');
    
    // Try secondary source
    try {
        return $this->getSecondaryData();
    } catch (Throwable $secondaryError) {
        // Log both errors
        Exceptions::createFromThrowable($secondaryError, 'Secondary DB');
        
        // Return minimal safe data
        return $this->getMinimalData();
    }
}
```

---

## 3. Systematic Bug Fixing Approach

### 3.1 Bug Investigation Process

#### Step 1: Reproduce the Issue
```php
// Create a test case to reproduce the bug
class ProductBugTest extends TestCase
{
    public function test_product_price_calculation_bug()
    {
        // Arrange: Set up the exact conditions
        $product = Product::factory()->create(['price' => 100]);
        $discount = Discount::factory()->create(['percentage' => 20]);
        
        // Act: Perform the action that causes the bug
        $calculatedPrice = $product->calculateDiscountedPrice($discount);
        
        // Assert: Verify the expected vs actual behavior
        $this->assertEquals(80, $calculatedPrice, 'Price calculation is incorrect');
    }
}
```

#### Step 2: Isolate the Problem
```php
// Add debug points to isolate the issue
public function calculateDiscountedPrice($discount)
{
    dump("Original price: {$this->price}"); // Debug point 1
    
    $discountAmount = $this->price * ($discount->percentage / 100);
    dump("Discount amount: $discountAmount"); // Debug point 2
    
    $finalPrice = $this->price - $discountAmount;
    dump("Final price: $finalPrice"); // Debug point 3
    
    return $finalPrice;
}
```

#### Step 3: Identify Root Cause
```php
// Use CloudCart's logging to track the issue
Log::channel('debug')->info('Price calculation debug', [
    'product_id' => $this->id,
    'original_price' => $this->price,
    'discount_percentage' => $discount->percentage,
    'calculated_price' => $finalPrice,
    'stack_trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS)
]);
```

### 3.2 Common Bug Patterns in CloudCart

#### Multi-Tenant Data Leakage
```php
// WRONG: Missing tenant isolation
$products = Product::all(); // Gets products from all tenants!

// CORRECT: Use CloudCart's tenant scoping
$products = Product::owner()->get(); // Only current tenant's products

// CORRECT: Explicit site filtering
$products = Product::where('site_id', site('site_id'))->get();
```

#### N+1 Query Problems
```php
// WRONG: Causes N+1 queries
foreach ($products as $product) {
    echo $product->category->name; // Lazy loading in loop
}

// CORRECT: Eager loading
$products = Product::with('category')->get();
foreach ($products as $product) {
    echo $product->category->name;
}

// CORRECT: Use CloudCart's optimized scopes
$products = Product::listingNew()->with('category')->get();
```

#### Memory Leaks in Large Operations
```php
// WRONG: Loading all records into memory
$products = Product::all();
foreach ($products as $product) {
    $this->processProduct($product);
}

// CORRECT: Use chunking
Product::chunk(1000, function ($products) {
    foreach ($products as $product) {
        $this->processProduct($product);
    }
});

// CORRECT: Use CloudCart's batch processing
$this->exec(function (Site $site) {
    Product::chunk(1000, function ($products) {
        // Process products for current site
    });
});
```

#### Queue Job Failures
```php
// WRONG: No error handling in jobs
public function handle()
{
    $this->processData(); // Can fail silently
}

// CORRECT: Proper error handling
public function handle()
{
    try {
        $this->processData();
        return static::EXECUTE_DESTROY;
    } catch (Throwable $e) {
        Exceptions::createFromThrowable($e, 'Job Processing');
        
        // Retry logic
        if ($this->attempts() < 3) {
            return [static::RESTART_DELAY, 300]; // Retry in 5 minutes
        }
        
        return static::DESTROY; // Give up after 3 attempts
    }
}
```

---

## 4. Refactoring Strategies

### 4.1 Code Smell Detection

#### Long Methods
```php
// BEFORE: Long method with multiple responsibilities
public function processOrder($orderId)
{
    // 50+ lines of mixed logic
    $order = Order::find($orderId);
    // Validation logic...
    // Payment processing...
    // Inventory updates...
    // Email notifications...
    // Analytics tracking...
}

// AFTER: Extracted into focused methods
public function processOrder($orderId)
{
    $order = $this->validateOrder($orderId);
    $this->processPayment($order);
    $this->updateInventory($order);
    $this->sendNotifications($order);
    $this->trackAnalytics($order);
}

private function validateOrder($orderId): Order
{
    // Focused validation logic
}

private function processPayment(Order $order): void
{
    // Focused payment logic
}
```

#### God Classes
```php
// BEFORE: God class handling everything
class ProductManager
{
    public function createProduct($data) { /* ... */ }
    public function updateInventory($productId, $quantity) { /* ... */ }
    public function calculatePrice($productId) { /* ... */ }
    public function generateSeo($productId) { /* ... */ }
    public function syncWithExternal($productId) { /* ... */ }
    // 20+ more methods...
}

// AFTER: Split into focused services
class ProductService
{
    public function createProduct($data): Product { /* ... */ }
    public function updateProduct(Product $product, $data): Product { /* ... */ }
}

class InventoryService
{
    public function updateQuantity($productId, $quantity): void { /* ... */ }
    public function checkAvailability($productId): bool { /* ... */ }
}

class PriceCalculator
{
    public function calculatePrice(Product $product): float { /* ... */ }
    public function calculateDiscountedPrice(Product $product, Discount $discount): float { /* ... */ }
}
```

#### Duplicate Code
```php
// BEFORE: Duplicated validation logic
class ProductController
{
    public function store(Request $request)
    {
        if (!$request->has('name') || strlen($request->name) < 3) {
            throw new Error('Name is required and must be at least 3 characters');
        }
        // More validation...
    }
}

class ProductImporter
{
    public function import($data)
    {
        if (!isset($data['name']) || strlen($data['name']) < 3) {
            throw new Error('Name is required and must be at least 3 characters');
        }
        // Same validation...
    }
}

// AFTER: Extracted to reusable validator
class ProductValidator
{
    public function validateName($name): void
    {
        if (!$name || strlen($name) < 3) {
            throw new Error('Name is required and must be at least 3 characters', 'name');
        }
    }
    
    public function validateProduct(array $data): void
    {
        $this->validateName($data['name'] ?? null);
        // Other validations...
    }
}
```

### 4.2 Performance Refactoring

#### Database Query Optimization
```php
// BEFORE: Inefficient queries
public function getProductsWithCategories()
{
    $products = Product::all();
    foreach ($products as $product) {
        $product->category_name = $product->category->name; // N+1 query
    }
    return $products;
}

// AFTER: Optimized with joins
public function getProductsWithCategories()
{
    return Product::select([
            'products.id',
            'products.name',
            'products.price',
            'categories.name as category_name'
        ])
        ->join('categories', 'products.category_id', '=', 'categories.id')
        ->get();
}

// EVEN BETTER: Use CloudCart's temp tables when available
public function getProductsWithCategories()
{
    $tempTable = Product::getTempTable();
    
    if ($tempTable) {
        // Use pre-computed data from temp table
        return DB::table($tempTable)
            ->select(['id', 'name', 'price', 'category_name'])
            ->get();
    }
    
    // Fallback to optimized query
    return $this->getProductsWithCategoriesOptimized();
}
```

#### Caching Refactoring
```php
// BEFORE: No caching
public function getPopularProducts()
{
    return Product::withCount('orders')
        ->orderBy('orders_count', 'desc')
        ->limit(10)
        ->get();
}

// AFTER: With caching
public function getPopularProducts()
{
    return CcCache::remember('popular_products', 3600, function () {
        return Product::withCount('orders')
            ->orderBy('orders_count', 'desc')
            ->limit(10)
            ->get();
    }, ['products', 'popular']);
}

// EVEN BETTER: Use CloudCart's cache patterns
public function getPopularProducts()
{
    $cacheKey = "popular_products";
    
    return CcCache::remember($cacheKey, 3600, function () {
        return Product::owner()
            ->withCount('orders')
            ->orderBy('orders_count', 'desc')
            ->limit(10)
            ->get();
    }, ['products', 'popular']);
}
```

### 4.3 Architecture Refactoring

#### Extract Service Classes
```php
// BEFORE: Business logic in controller
class OrderController extends Controller
{
    public function create(Request $request)
    {
        // 100+ lines of order creation logic
        $order = new Order();
        $order->customer_id = $request->customer_id;
        // Complex calculation logic...
        // Payment processing...
        // Inventory updates...
        // Email sending...
        $order->save();
        
        return response()->json($order);
    }
}

// AFTER: Extracted to service
class OrderController extends Controller
{
    public function __construct(private OrderService $orderService)
    {
    }
    
    public function create(OrderRequest $request)
    {
        $order = $this->orderService->createOrder($request->validated());
        
        return new OrderResource($order);
    }
}

class OrderService
{
    public function createOrder(array $data): Order
    {
        DB::transaction(function () use ($data) {
            $order = $this->buildOrder($data);
            $this->processPayment($order);
            $this->updateInventory($order);
            $this->sendNotifications($order);
            
            return $order;
        });
    }
    
    private function buildOrder(array $data): Order
    {
        // Focused order building logic
    }
}
```

#### Implement Repository Pattern
```php
// BEFORE: Direct Eloquent usage everywhere
class ProductController
{
    public function index()
    {
        return Product::where('active', 'yes')->paginate(20);
    }
}

// AFTER: Repository pattern
interface ProductRepositoryInterface
{
    public function getActive(int $perPage = 20): LengthAwarePaginator;
    public function findByCategory(int $categoryId): Collection;
}

class ProductRepository implements ProductRepositoryInterface
{
    public function getActive(int $perPage = 20): LengthAwarePaginator
    {
        return Product::owner()
            ->where('active', 'yes')
            ->where('draft', 'no')
            ->paginate($perPage);
    }
    
    public function findByCategory(int $categoryId): Collection
    {
        return Product::owner()
            ->where('category_id', $categoryId)
            ->where('active', 'yes')
            ->get();
    }
}

class ProductController
{
    public function __construct(private ProductRepositoryInterface $productRepository)
    {
    }
    
    public function index()
    {
        return $this->productRepository->getActive();
    }
}
```

---

## 5. Testing and Validation

### 5.1 Bug Fix Testing

#### Test-Driven Bug Fixing
```php
// Step 1: Write a failing test that reproduces the bug
class ProductPriceBugTest extends TestCase
{
    public function test_product_price_with_discount_calculation()
    {
        // Arrange
        $product = Product::factory()->create(['price' => 100.00]);
        $discount = Discount::factory()->create(['percentage' => 20]);
        
        // Act
        $discountedPrice = $product->calculateDiscountedPrice($discount);
        
        // Assert - This should fail initially
        $this->assertEquals(80.00, $discountedPrice);
    }
}

// Step 2: Fix the bug
// Step 3: Verify the test passes
```

#### Regression Testing
```php
// Create tests to prevent regression
class ProductRegressionTest extends TestCase
{
    public function test_product_price_calculation_edge_cases()
    {
        // Test zero price
        $product = Product::factory()->create(['price' => 0]);
        $discount = Discount::factory()->create(['percentage' => 20]);
        $this->assertEquals(0, $product->calculateDiscountedPrice($discount));
        
        // Test 100% discount
        $product = Product::factory()->create(['price' => 100]);
        $discount = Discount::factory()->create(['percentage' => 100]);
        $this->assertEquals(0, $product->calculateDiscountedPrice($discount));
        
        // Test negative price (should not happen but test anyway)
        $product = Product::factory()->create(['price' => -10]);
        $discount = Discount::factory()->create(['percentage' => 20]);
        $this->assertEquals(-8, $product->calculateDiscountedPrice($discount));
    }
}
```

### 5.2 Refactoring Validation

#### Before/After Performance Tests
```php
class PerformanceTest extends TestCase
{
    public function test_product_listing_performance()
    {
        // Create test data
        Product::factory()->count(1000)->create();
        
        // Test old implementation
        $start = microtime(true);
        $oldResult = $this->getProductsOldWay();
        $oldTime = microtime(true) - $start;
        
        // Test new implementation
        $start = microtime(true);
        $newResult = $this->getProductsNewWay();
        $newTime = microtime(true) - $start;
        
        // Assert performance improvement
        $this->assertLessThan($oldTime, $newTime, 'New implementation should be faster');
        $this->assertEquals($oldResult->count(), $newResult->count(), 'Results should be identical');
    }
}
```

#### Memory Usage Tests
```php
public function test_memory_usage_improvement()
{
    $startMemory = memory_get_usage(true);
    
    // Your refactored code
    $this->processLargeDataset();
    
    $endMemory = memory_get_usage(true);
    $memoryUsed = $endMemory - $startMemory;
    
    // Assert memory usage is within acceptable limits
    $this->assertLessThan(50 * 1024 * 1024, $memoryUsed, 'Memory usage should be under 50MB');
}
```

---

## 6. Code Quality Tools

### 6.1 Static Analysis

#### PHPStan Configuration
```php
// Use PHPStan for static analysis
// phpstan.neon
parameters:
    level: 6
    paths:
        - app
        - modules
    excludePaths:
        - app/Integration/*/Lib/*
    checkMissingIterableValueType: false
    checkGenericClassInNonGenericObjectType: false
```

#### Rector for Automated Refactoring
```php
// rector.php - Use for automated refactoring
use Rector\Config\RectorConfig;
use Rector\Set\ValueObject\LevelSetList;

return static function (RectorConfig $rectorConfig): void {
    $rectorConfig->paths([
        __DIR__ . '/app',
        __DIR__ . '/modules',
    ]);
    
    $rectorConfig->sets([
        LevelSetList::UP_TO_PHP_84
    ]);
};
```

### 6.2 Code Style and Standards

#### Laravel Pint Configuration
```json
// pint.json
{
    "preset": "laravel",
    "rules": {
        "declare_strict_types": true,
        "no_unused_imports": true,
        "ordered_imports": true,
        "single_quote": true
    }
}
```

#### Custom Code Standards
```php
// Follow CloudCart's coding standards
declare(strict_types=1);

namespace App\Services;

use App\Models\Product\Product;
use App\Exceptions\Error;
use Illuminate\Support\Collection;

/**
 * Product service for handling business logic
 */
class ProductService
{
    /**
     * Create a new product with validation
     */
    public function createProduct(array $data): Product
    {
        $this->validateProductData($data);
        
        return Product::create($data);
    }
    
    /**
     * Validate product data
     * 
     * @throws Error
     */
    private function validateProductData(array $data): void
    {
        if (empty($data['name'])) {
            throw new Error('Product name is required', 'name');
        }
        
        if (!isset($data['price']) || $data['price'] < 0) {
            throw new Error('Valid price is required', 'price');
        }
    }
}
```

---

## 7. Monitoring and Prevention

### 7.1 Error Monitoring

#### Sentry Integration
```php
// CloudCart uses Sentry for error tracking
use App\Traits\SentryLog;

class MyService
{
    use SentryLog;
    
    public function riskyOperation()
    {
        try {
            // Risky code
        } catch (Throwable $e) {
            $this->sentryError($e, [
                'context' => 'risky_operation',
                'user_id' => auth()->id(),
                'site_id' => site('site_id')
            ]);
            
            throw $e;
        }
    }
}
```

#### Custom Logging
```php
// Use CloudCart's logging patterns
Log::channel('debug')->info('Operation started', [
    'operation' => 'product_sync',
    'site_id' => site('site_id'),
    'user_id' => auth()->id()
]);

// Log performance metrics
Log::channel('performance')->info('Slow query detected', [
    'query_time' => $executionTime,
    'query' => $sql,
    'bindings' => $bindings
]);
```

### 7.2 Preventive Measures

#### Code Review Checklist
```php
/**
 * Code Review Checklist for CloudCart:
 * 
 * □ Multi-tenant isolation (using owner() scope or site_id filtering)
 * □ Proper error handling with CloudCart exceptions
 * □ Performance considerations (N+1 queries, memory usage)
 * □ Security (input validation, SQL injection prevention)
 * □ Caching where appropriate
 * □ Queue jobs have proper error handling
 * □ Tests cover the main functionality
 * □ Documentation is updated
 * □ Follows CloudCart coding standards
 * □ Namespace-specific patterns are followed
 */
```

#### Automated Quality Gates
```php
// Use in CI/CD pipeline
// .gitlab-ci.yml
test:
  script:
    - php artisan test --coverage --min=80
    - vendor/bin/phpstan analyse --level=6
    - vendor/bin/pint --test
    - vendor/bin/rector --dry-run
```

---

## 8. Common Refactoring Patterns

### 8.1 Legacy Code Modernization

#### PHP 8+ Features
```php
// BEFORE: Old PHP patterns
class ProductService
{
    private $repository;
    private $validator;
    
    public function __construct(ProductRepository $repository, ProductValidator $validator)
    {
        $this->repository = $repository;
        $this->validator = $validator;
    }
    
    public function createProduct($data)
    {
        if ($data === null) {
            return null;
        }
        
        return $this->repository->create($data);
    }
}

// AFTER: Modern PHP 8+ patterns
class ProductService
{
    public function __construct(
        private readonly ProductRepository $repository,
        private readonly ProductValidator $validator
    ) {
    }
    
    public function createProduct(?array $data): ?Product
    {
        return $data ? $this->repository->create($data) : null;
    }
    
    public function getProductStatus(Product $product): string
    {
        return match ($product->status) {
            'active' => 'Available',
            'inactive' => 'Unavailable',
            'draft' => 'Coming Soon',
            default => 'Unknown'
        };
    }
}
```

#### Collection Usage
```php
// BEFORE: Manual array processing
public function getActiveProductNames($products)
{
    $names = [];
    foreach ($products as $product) {
        if ($product->active === 'yes') {
            $names[] = $product->name;
        }
    }
    return $names;
}

// AFTER: Collection methods
public function getActiveProductNames(Collection $products): Collection
{
    return $products
        ->filter(fn(Product $product) => $product->active === 'yes')
        ->pluck('name');
}
```

### 8.2 CloudCart-Specific Patterns

#### Temp Table Optimization
```php
// BEFORE: Always using complex queries
public function getProductListing()
{
    return Product::with(['variants', 'discounts', 'category'])
        ->where('active', 'yes')
        ->orderBy('price')
        ->paginate(20);
}

// AFTER: Use temp tables when available
public function getProductListing()
{
    $tempTable = Product::getTempTable();
    
    if ($tempTable) {
        return DB::table($tempTable)
            ->where('has_enable_sell', 1)
            ->orderBy('order_min_price_with_discounted_asc_id_asc')
            ->paginate(20);
    }
    
    // Fallback to complex query
    return Product::listingNew()
        ->with(['variants', 'discounts', 'category'])
        ->paginate(20);
}
```

#### Queue Job Patterns
```php
// BEFORE: Synchronous processing
public function processProducts($productIds)
{
    foreach ($productIds as $productId) {
        $this->processProduct($productId);
    }
}

// AFTER: Asynchronous with proper error handling
public function processProducts(array $productIds): void
{
    foreach ($productIds as $productId) {
        ProcessProductJob::dispatch($productId)
            ->onQueue('product-processing')
            ->delay(now()->addSeconds(5));
    }
}

class ProcessProductJob extends Job
{
    public function handle(): int
    {
        try {
            $this->processProduct();
            return static::EXECUTE_DESTROY;
        } catch (Throwable $e) {
            Exceptions::createFromThrowable($e, 'Product Processing');
            
            if ($this->attempts() < 3) {
                return [static::RESTART_DELAY, 300];
            }
            
            return static::DESTROY;
        }
    }
}
```

---

## Best Practices Summary

### 1. Always Consider Multi-Tenancy
- Use `owner()` scope or explicit `site_id` filtering
- Test with multiple tenants
- Verify data isolation

### 2. Follow CloudCart's Error Handling
- Use custom exceptions (`Error`, `Fault`, etc.)
- Log to `Exceptions` model for tracking
- Provide meaningful error messages

### 3. Optimize for Performance
- Use temp tables when available
- Implement proper caching
- Avoid N+1 queries
- Monitor memory usage

### 4. Test Thoroughly
- Write tests for bug fixes
- Include edge cases
- Test performance improvements
- Verify multi-tenant isolation

### 5. Use CloudCart's Tools
- Leverage existing debug helpers
- Follow established patterns
- Use the queue system properly
- Implement proper logging

This comprehensive guide should help developers effectively debug, fix bugs, and refactor code in the CloudCart platform while maintaining its architectural integrity and performance characteristics.
