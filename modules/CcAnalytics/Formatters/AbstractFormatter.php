<?php

declare(strict_types=1);

namespace Modules\CcAnalytics\Formatters;

use App\Locale\WindowsZones;
use App\Models\Setting\Configuration;
use Carbon\Carbon;
use Closure;
use Illuminate\Support\Collection;
use Modules\CcAnalytics\Constants;
use Modules\CcAnalytics\Responses\CustomCompareChart;
use Modules\CcAnalytics\Responses\NoCompareChart;
use Modules\CcAnalytics\Responses\PeriodCompareChart;
use Modules\CcAnalytics\Responses\RecordKeyCompareChart;
use Modules\CcAnalytics\Responses\TableDeviceResponse;
use Modules\CcAnalytics\Responses\NoDataTableDeviceResponse;
use Modules\CcAnalytics\Responses\TableResponse;

abstract class AbstractFormatter
{
    /**
     * @var \Carbon\Carbon[]
     */
    public $interval;

    protected static string $groupType;

    protected \Illuminate\Config\Repository $settings;

    /** @var string $details */
    protected $details;

    /** @var mixed $record */
    protected $record;

    /** @var boolean $export */
    protected $export = false;

    /**
     * @param array<Carbon, Carbon> $interval
     * @param string $compareType
     * @param string $groupType
     */
    public function __construct(array $interval, protected string $compareType, string $groupType = 'auto')
    {
        $this->interval = $interval;
        self::$groupType = $this->validateGroupType($groupType);
        $this->settings = Configuration::getByGroup(Constants::CACHE_CONFIG);
    }

    /**
     * @return array
     */
    public function output(): array
    {
        if ($pre = $this->preOutput()) {
            return $pre;
        }

        $methodName = sprintf('%sCompare', in_array($this->compareType, ['period', 'year']) ? 'period' : $this->compareType);
        return $this->$methodName();
    }

    /**
     * @return array
     */
    public function details(): array
    {
        if ($pre = $this->preDetails()) {
            return $pre;
        }

        $methodName = sprintf('%sDetailsCompare', in_array($this->compareType, ['period', 'year']) ? 'period' : $this->compareType);
        return $this->$methodName();
    }

    /**
     * @return array
     */
    public function viewMore(): array
    {
        if ($pre = $this->preViewMore()) {
            return $pre;
        }

        $methodName = sprintf('%sViewMoreCompare', in_array($this->compareType, ['period', 'year']) ? 'period' : $this->compareType);
        return $this->$methodName();
    }

    /**
     * @return array
     */
    public function subDashboard(): array
    {
        if ($pre = $this->preSubDashboard()) {
            return $pre;
        }

        $methodName = sprintf('%sSubDashboardCompare', in_array($this->compareType, ['period', 'year']) ? 'period' : $this->compareType);
        return $this->$methodName();
    }

    /**
     * @return array
     */
    public function subDetails(): array
    {
        if ($pre = $this->preSubDetails()) {
            return $pre;
        }

        $methodName = sprintf('%sSubDetailsCompare', in_array($this->compareType, ['period', 'year']) ? 'period' : $this->compareType);
        return $this->$methodName();
    }

    /**
     * @return array
     */
    public function moreDetails(): array
    {
        if ($pre = $this->preMoreDetails()) {
            return $pre;
        }

        $methodName = sprintf('%sMoreDetailsCompare', in_array($this->compareType, ['period', 'year']) ? 'period' : $this->compareType);
        return $this->$methodName();
    }

    /**
     * @return string
     */
    public static function getGroupType(): string
    {
        return self::$groupType;
    }

    /**
     * @param string $groupType
     */
    public static function setGroupType(string $groupType): void
    {
        self::$groupType = $groupType;
    }

    /**
     * @return array|null
     */
    public static function getGroupFormat(): string
    {
        $groupType = self::getGroupType();
        if ($groupType == Constants::GROUP_BY_NONE) {
            return '0';
        } elseif ($groupType == Constants::GROUP_BY_QUARTER) {
            return Constants::FORMAT_BY_MONTH;
        }

        return Constants::GROUP_FORMATS[$groupType] ?? Constants::FORMAT_BY_DAY;
    }

    /**
     * @param float $cur
     * @param float $prev
     * @return float|int
     */
    public static function calculatePercentage(float $cur, float $prev)
    {
        if ($cur > $prev) {
            if ($prev <> 0) {
                return (($cur / $prev) * 100) - 100;
            }

            return 100;
        } elseif ($cur == $prev) {
            return 0;
        }

        if ($cur <> 0) {
            return ((($prev / $cur) * 100) - 100) * -1;
        }

        return -100;
    }

    /**
     * @param Carbon $date
     * @param Carbon $from
     * @param Carbon $to
     * @return string
     */
    public static function guessGroup(Carbon $date, Carbon $from, Carbon $to): string
    {
        $groupType = self::getGroupType();
        if ($groupType == Constants::GROUP_BY_AUTO) {
            $type = static::guessGroupType($from->clone()->startOfDay(), $to->clone()->endOfDay());
            return $date[Constants::GROUP_FORMATS[$type] ?? Constants::GROUP_FORMATS[Constants::GROUP_BY_DAY]];
            //            return static::guessGroupFromDiff($date, $from->clone()->startOfDay()->diffInDays($to->clone()->endOfDay()) + 1);
        } elseif ($groupType == Constants::GROUP_BY_NONE) {
            return $date->format('0');
        } elseif ($groupType == Constants::GROUP_BY_QUARTER) {
            return $date->startOfQuarter()->format('Y-m');
        }

        return $date->format(Constants::GROUP_FORMATS[$groupType] ?? Constants::FORMAT_BY_DAY);
    }

    //    /**
    //     * @param Carbon $date
    //     * @param int $diff
    //     * @return string
    //     */
    //    protected static function guessGroupFromDiff(Carbon $date, int $diff): string
    //    {
    //        if($diff > 730) {
    //            self::setGroupType(Constants::GROUP_BY_YEAR);
    //            return $date->clone()->format(Constants::FORMAT_BY_YEAR);
    //        } elseif($diff > 90) {
    //            self::setGroupType(Constants::GROUP_BY_MONTH);
    //            return $date->clone()->format(Constants::FORMAT_BY_MONTH);
    //        } elseif($diff > 60) {
    //            self::setGroupType(Constants::GROUP_BY_WEEK);
    //            return $date->clone()->format(Constants::FORMAT_BY_WEEK);
    //        } elseif($diff < 3) {
    //            self::setGroupType(Constants::GROUP_BY_HOUR);
    //            return $date->clone()->format(Constants::FORMAT_BY_HOUR);
    //        }
    //
    //        self::setGroupType(Constants::GROUP_BY_DAY);
    //        return $date->clone()->format(Constants::FORMAT_BY_DAY);
    //    }

    /**
     * @param Carbon $from
     * @param Carbon $to
     * @return string
     */
    public static function guessGroupTypeMongoDb(Carbon $from, Carbon $to): string
    {
        $type = static::guessGroupType($from, $to);
        if ($type == Constants::GROUP_BY_YEAR) {
            return Constants::MONGO_GROUP_BY_YEAR;
        } elseif ($type == Constants::GROUP_BY_MONTH) {
            return Constants::MONGO_GROUP_BY_MONTH;
        } elseif ($type == Constants::GROUP_BY_HOUR) {
            return Constants::MONGO_GROUP_BY_HOUR;
        } elseif ($type == Constants::GROUP_BY_WEEK) {
            return Constants::MONGO_GROUP_BY_WEEK;
        } elseif ($type == Constants::GROUP_BY_QUARTER) {
            return Constants::MONGO_GROUP_BY_QUARTER;
        }

        return Constants::MONGO_GROUP_BY_DAY;
    }

    /**
     * @param Carbon $from
     * @param Carbon $to
     * @return array|null
     */
    public static function getRangeDateForMongoDb(Carbon $from, Carbon $to): ?array
    {
        $intervals = [];
        $groupType = self::getGroupType();

        $className = sprintf('\Modules\CcAnalytics\Helpers\TableIntervals\TableInterval%s', ucfirst($groupType));
        if (!class_exists($className)) {
            return $intervals;
        }

        $calculator = new $className($from->clone(), $to->clone());
        return $calculator->getIntervals();
    }

    /**
     * @param Carbon $from
     * @param Carbon $to
     * @return array|null
     */
    public static function getGroupDateForMongoDb(Carbon $from, Carbon $to): ?array
    {
        $groupType = self::getGroupType();
        if ($groupType == Constants::GROUP_BY_AUTO) {
            return [
                '$dateToString' => [
                    'format' => self::guessGroupTypeMongoDb($from->clone()->startOfDay(), $to->clone()->endOfDay()),
                    'date' => '$date',
                    'timezone' => WindowsZones::etcTimezoneForMongo(site('timezone', 'UTC'))
                ]
            ];
        } elseif ($groupType == Constants::GROUP_BY_QUARTER) {
            return [
                '$switch' => [
                    'branches' => [
                        [
                            'case' => [
                                '$in' => [['$month' => ['date' => '$date', 'timezone' => WindowsZones::etcTimezoneForMongo(site('timezone', 'UTC'))]], [1,2,3]],
                            ],
                            'then' => [
                                '$concat' => [
                                    [
                                        '$convert' => [
                                            'input' => ['$year' => ['date' => '$date', 'timezone' => WindowsZones::etcTimezoneForMongo(site('timezone', 'UTC'))]],
                                            'to' => 'string',
                                        ]
                                    ], '-Jan'
                                ]
                            ]
                        ],
                        [
                            'case' => [
                                '$in' => [['$month' => ['date' => '$date', 'timezone' => WindowsZones::etcTimezoneForMongo(site('timezone', 'UTC'))]], [4,5,6]],
                            ],
                            'then' => [
                                '$concat' => [
                                    [
                                        '$convert' => [
                                            'input' => ['$year' => ['date' => '$date', 'timezone' => WindowsZones::etcTimezoneForMongo(site('timezone', 'UTC'))]],
                                            'to' => 'string',
                                        ]
                                    ], '-Apr'
                                ]
                            ]
                        ],
                        [
                            'case' => [
                                '$in' => [['$month' => ['date' => '$date', 'timezone' => WindowsZones::etcTimezoneForMongo(site('timezone', 'UTC'))]], [7,8,9]],
                            ],
                            'then' => [
                                '$concat' => [
                                    [
                                        '$convert' => [
                                            'input' => ['$year' => ['date' => '$date', 'timezone' => WindowsZones::etcTimezoneForMongo(site('timezone', 'UTC'))]],
                                            'to' => 'string',
                                        ]
                                    ], '-Jul'
                                ]
                            ]
                        ],
                        [
                            'case' => [
                                '$in' => [['$month' => ['date' => '$date', 'timezone' => WindowsZones::etcTimezoneForMongo(site('timezone', 'UTC'))]], [10,11,12]],
                            ],
                            'then' => [
                                '$concat' => [
                                    [
                                        '$convert' => [
                                            'input' => ['$year' => ['date' => '$date', 'timezone' => WindowsZones::etcTimezoneForMongo(site('timezone', 'UTC'))]],
                                            'to' => 'string',
                                        ]
                                    ], '-Oct'
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        } elseif ($groupType == Constants::GROUP_BY_NONE) {
            return null;
        }

        return [
            '$dateToString' => [
                'format' => Constants::MONGO_FORMATS[$groupType],
                'date' => '$date',
                'timezone' => WindowsZones::etcTimezoneForMongo(site('timezone', 'UTC'))
            ]
        ];
    }

    /**
     * @param Carbon $from
     * @param Carbon $to
     * @return string
     */
    public static function guessGroupType(Carbon $from, Carbon $to): string
    {
        $diff = $from->diffInDays($to) + 1;
        if ($diff > 730) {
            self::setGroupType(Constants::GROUP_BY_YEAR);
            return self::getGroupType();
        } elseif ($diff > 90) {
            self::setGroupType(Constants::GROUP_BY_MONTH);
            return self::getGroupType();
        } elseif ($diff > 60) {
            self::setGroupType(Constants::GROUP_BY_WEEK);
            return self::getGroupType();
        } elseif ($diff < 3) {
            self::setGroupType(Constants::GROUP_BY_HOUR);
            return self::getGroupType();
        }

        self::setGroupType(Constants::GROUP_BY_DAY);
        return self::getGroupType();
    }

    /**
     * @return string
     */
    public function getDetails(): string
    {
        return $this->details;
    }

    /**
     * @param string $details
     * @return AbstractFormatter
     */
    public function setDetails(string $details): AbstractFormatter
    {
        $this->details = $details;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getRecord()
    {
        return $this->record;
    }

    /**
     * @param mixed $record
     * @return AbstractFormatter
     */
    public function setRecord($record): AbstractFormatter
    {
        $this->record = $record;
        return $this;
    }

    /**
     * @return bool
     */
    public function isExport(): bool
    {
        return $this->export;
    }

    /**
     * @param bool $export
     * @return AbstractFormatter
     */
    public function setExport(bool $export): AbstractFormatter
    {
        $this->export = $export;
        return $this;
    }

    /**
     * @return string
     */
    public function getCompareType(): string
    {
        return $this->compareType;
    }

    /**
     * @return array
     */
    abstract protected function noCompare(): array;

    /**
     * @return array
     */
    abstract protected function periodCompare(): array;

    /**
     * @return array
     */
    protected function noDetailsCompare(): array
    {
        return [];
    }

    /**
     * @return array
     */
    protected function periodDetailsCompare(): array
    {
        return [];
    }

    /**
     * @return array
     */
    protected function noViewMoreCompare(): array
    {
        return [];
    }

    /**
     * @return array
     */
    protected function periodViewMoreCompare(): array
    {
        return [];
    }

    /**
     * @return array
     */
    protected function noSubDashboardCompare(): array
    {
        return [];
    }

    /**
     * @return array
     */
    protected function periodSubDashboardCompare(): array
    {
        return [];
    }

    /**
     * @return array
     */
    protected function noSubDetailsCompare(): array
    {
        return [];
    }

    /**
     * @return array
     */
    protected function periodSubDetailsCompare(): array
    {
        return [];
    }

    /**
     * @return array
     */
    protected function noMoreDetailsCompare(): array
    {
        return [];
    }

    /**
     * @return array
     */
    protected function periodMoreDetailsCompare(): array
    {
        return [];
    }

    /**
     * @return array|null
     */
    protected function preOutput(): ?array
    {
        return null;
    }

    /**
     * @return array|null
     */
    protected function preDetails(): ?array
    {
        return null;
    }

    /**
     * @return array|null
     */
    protected function preViewMore(): ?array
    {
        return null;
    }

    /**
     * @return array|null
     */
    protected function preSubDashboard(): ?array
    {
        return null;
    }

    /**
     * @return array|null
     */
    protected function preSubDetails(): ?array
    {
        return null;
    }

    /**
     * @return array|null
     */
    protected function preMoreDetails(): ?array
    {
        return null;
    }

    /**
     * @param Collection $records
     * @param Closure $callback
     * @return PeriodCompareChart
     */
    protected function periodCompareChart(Collection $records, Closure $callback): PeriodCompareChart
    {
        $formatter = new PeriodCompareChart($records, $this->interval[0]->clone()->shiftTimezone('UTC'), $this->interval[1]->clone()->shiftTimezone('UTC'));
        $formatter->setCompareType($this->getCompareType());
        if ($formatter->getCompareType() == 'year') {
            $formatter->setYears(intval($this->interval[4]));
        }

        $callback($formatter);
        return $formatter;
    }

    /**
     * @param Collection $records
     * @param Closure $callback
     * @return NoCompareChart
     */
    protected function noCompareChart(Collection $records, Closure $callback): NoCompareChart
    {
        $formatter = new NoCompareChart($records, $this->interval[0]->clone()->shiftTimezone('UTC'), $this->interval[1]->clone()->shiftTimezone('UTC'));
        $callback($formatter);
        return $formatter;
    }

    /**
     * @param Collection $records
     * @param Closure $callback
     * @return CustomCompareChart
     */
    protected function customCompareChart(Collection $records, Closure $callback): CustomCompareChart
    {
        $formatter = new CustomCompareChart($records, $this->interval[0]->clone()->shiftTimezone('UTC'), $this->interval[1]->clone()->shiftTimezone('UTC'));
        $callback($formatter);
        return $formatter;
    }

    /**
     * @param Collection $records
     * @param Closure $callback
     * @return RecordKeyCompareChart
     */
    protected function recordKeyCompareChart(Collection $records, Closure $callback): RecordKeyCompareChart
    {
        $formatter = new RecordKeyCompareChart($records);
        $callback($formatter);
        return $formatter;
    }

    /**
     * @param Closure $callback
     * @return TableResponse
     */
    protected function dashboardTable(Closure $callback): TableResponse
    {
        $formatter = new TableResponse();
        $callback($formatter);
        return $formatter;
    }

    /**
     * @param string $value
     * @return string
     */
    protected function validateGroupType(string $value): string
    {
        if (in_array($value, Constants::GROUP_BY_KEYS)) {
            return $value;
        }

        return Constants::GROUP_BY_AUTO;
    }

    /**
     * @param string $key
     * @param null $default
     * @return mixed
     */
    protected function setting(string $key, $default = null)
    {
        return $this->settings->get($key, $default);
    }

    /**
     * @param int $total
     * @param int $mobile
     * @param int $desktop
     * @return TableDeviceResponse
     */
    protected function deviceFormatter(int $total, int $mobile, int $desktop): TableDeviceResponse
    {
        if ($this->interval[0]->clone()->lt(Carbon::create(2023, 1, 17))) {
            return new NoDataTableDeviceResponse($total, $mobile, $desktop);
        }

        return new TableDeviceResponse($total, $mobile, $desktop);
    }
}
