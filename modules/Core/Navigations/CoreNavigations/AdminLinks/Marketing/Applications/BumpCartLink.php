<?php

declare(strict_types=1);

namespace Modules\Core\Navigations\CoreNavigations\AdminLinks\Marketing\Applications;

use Modules\Core\Navigations\CoreNavigations\AbstractLink;

class BumpCartLink extends AbstractLink
{
    #[\Override]
    public function getName(): string
    {
        return 'marketing.applications.bumpcart';
    }

    #[\Override]
    public function getLabel(): string
    {
        return 'Bumpcart';
    }

    #[\Override]
    public function getUrl(): ?string
    {
        return route('apps.bumper_offer');
    }
}
