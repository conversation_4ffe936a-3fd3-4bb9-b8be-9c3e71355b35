<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 8.6.2018 г.
 * Time: 10:41 ч.
 */

namespace Modules\SiteIntegrations\ListingColors;

use App\Models\Product\ParameterOption;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use Illuminate\Database\Eloquent\Builder;

class ListingColorsManager
{
    /**
     * @param Builder $query
     * @return void
     */
    public static function productListingExtend(Builder $query): void
    {
        if (
            !in_array(site('site_id'), config('listing.colors', [])) ||
            app_namespace() != 'site'
        ) {
            return;
        }

        $query->with(['listingVariants' => function ($query): void {
            /** @var Variant $query */
            $query->has('v1r');
            for ($i = 1; $i <= 3; $i++) {
                $query->with(sprintf('v%dr.parameter', $i));
            }
        }]);
    }

    /**
     * @param Product $product
     * @return Product
     */
    public static function productFormat(Product $product): Product
    {
        if (
            !in_array(site('site_id'), config('listing.colors', [])) ||
            $product->caller == 'details' ||
            !$product->relationLoaded('listingVariants') ||
            $product->listingVariants->isEmpty() ||
            app_namespace() != 'site'
        ) {
            return $product;
        }

        $colors = collect();
        if ($total = $product->total_variants) {
            $product->listingVariants->map(function (Variant $variant) use ($product, $total, &$colors): \App\Models\Product\Variant {
                for ($i = 1; $i <= $total; $i++) {
                    /** @var ParameterOption $parameterValue */
                    if (($parameterValue = $variant->getRelationValue(sprintf('v%dr', $i))) && ($parameter = ($parameterValue->parameter ?? null)) && $parameter->type == 'color') {
                        $colors->put($parameterValue->id, $parameterValue);
                    }
                }

                return $variant;
            });
        }

        $product->setAttribute('colors', $colors->values());

        return $product->syncOriginal();
    }

}
