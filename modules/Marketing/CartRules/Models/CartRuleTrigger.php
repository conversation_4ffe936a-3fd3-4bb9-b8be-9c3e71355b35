<?php

declare(strict_types=1);

namespace Modules\Marketing\CartRules\Models;

use Eloquent;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * @property integer $id
 * @property integer $cart_rule_id
 * @property integer $cart_rule_row_id
 * @property integer $sorting
 * @property-read Collection<CartRuleTriggerCondition> $conditions
 */
class CartRuleTrigger extends Eloquent
{
    protected $table = '@cart_rule_trigger';

    protected $guarded = [
        'id'
    ];

    protected $fillable = [
        'cart_rule_id', 'cart_rule_row_id', 'sorting',
    ];

    public $timestamps = false;

    public function conditions(): HasMany
    {
        return $this->hasMany(CartRuleTriggerCondition::class, 'cart_rule_trigger_id')
            ->orderBy('sorting', 'asc');
    }

}
