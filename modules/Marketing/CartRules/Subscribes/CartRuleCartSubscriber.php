<?php

declare(strict_types=1);

namespace Modules\Marketing\CartRules\Subscribes;

use App\Helper\Store\CartMessage;
use App\Helper\Store\Contracts\ItemContract;
use App\Helper\Store\Contracts\OrderContract;
use App\Helper\Store\Modifications\CartItemModification;
use App\Helper\Store\Modifications\TotalModification;
use Apps;
use App\Events\Models\CartGetProducts;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Support\Collection;
use Modules\Marketing\CartRules\CartRuleManager;
use Modules\Marketing\CartRules\Helpers\CartRuleMatches;

class CartRuleCartSubscriber
{
    /**
     * @param \App\Events\Models\CartGetProducts $event
     * @return mixed
     */
    public function onCartGetProducts(CartGetProducts $event): void
    {
        if (!Apps::installed(CartRuleManager::APP_KEY)) {
            return;
        }

        $messages = CartRuleMatches::make($event->cart, $event->products)->getNotifications();
        if (!empty($messages) && is_array($messages)) {
            array_map(function ($message) use ($event): void {
                $event->cart->setMessage(new CartMessage($message));
            }, $messages);
        }

        if (empty($matches = CartRuleMatches::make($event->cart, $event->products)->getMatches()) || !is_array($matches)) {
            return;
        }

        //        dd($matches, CartRuleMatches::make($event->cart, $event->products)->getNotifications());

        $groups = collect($matches)->groupBy('type');
        if ($groups->has('cart')) {
            $this->appendCartMatch($groups->get('cart'), $event->cart);
        }

        if ($groups->has('product')) {
            $this->appendCartProductsMatch($groups->get('product'), $event->products);
        }
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @param Dispatcher $events
     */
    public function subscribe(Dispatcher $events): void
    {
        $events->listen(
            CartGetProducts::class,
            $this->onCartGetProducts(...)
        );
    }

    /**
     * @param Illuminate\Support\Collection $matches
     * @param \App\Helper\Store\Contracts\OrderContract $order
     * @return mixed
     */
    protected function appendCartMatch(Collection $matches, OrderContract $order)
    {
        if (!$match = $matches->sortByDesc('value')->first()) {
            return;
        }

        $order->setModificationTotal(new TotalModification(array_merge($match, [
            'key' => sprintf('cart-rule-%s-%d', $match['value_type'], $match['id']),
            'type' => $match['action_type'],
            'type_value' => $match['value_type'],
            'name' => $match['title'] ?: $match['name'],
            'group' => 'cart',
        ])));
    }

    /**
     * @param Illuminate\Support\Collection $matches
     * @param Illuminate\Support\Collection $products
     * @return mixed
     */
    protected function appendCartProductsMatch(Collection $matches, Collection $products)
    {
        foreach ($matches as $match) {
            if (empty($match['actionProducts'])) {
                continue;
            }

            $this->appendCartProductsDiscount($match, $products->whereIn('key', $match['actionProducts']));
        }
    }

    /**
     * @param array $match
     * @param Illuminate\Support\Collection $products
     * @return mixed
     */
    protected function appendCartProductsDiscount(array $match, Collection $products)
    {
        $products->transform(function (ItemContract $item) use ($match): \App\Helper\Store\Contracts\ItemContract {
            $modification = new CartItemModification(array_merge($match, [
                'key' => sprintf('cart-rule-%s-%d', $match['value_type'], $match['id']),
                'type' => $match['action_type'],
                'type_value' => $match['value_type'],
                'name' => $match['title'] ?: $match['name'],
                'group' => 'product',
            ]));

            if ($this->allowAppendProductModification($item, $modification)) {
                $item->setModification($modification);
            }

            return $item;
        });
    }

    /**
     * @param \App\Helper\Store\Contracts\ItemContract $item
     * @param \App\Helper\Store\Modifications\CartItemModification $modification
     * @return mixed
     */
    protected function allowAppendProductModification(ItemContract $item, CartItemModification $modification): bool
    {
        if (!in_array($modification->getTypeValue(), ['percent', 'amount'])) {
            return false;
        }

        if ($modification->getTypeValue() == 'percent') {
            return true;
        }

        if ($item->getQuantity() <= 0) {
            return false;
        }

        return $modification->getValue() <= $item->getTotalPriceWithOptionsAfterDiscounts() / $item->getQuantity();
    }
}
