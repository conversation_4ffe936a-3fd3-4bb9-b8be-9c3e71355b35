<?php

declare(strict_types=1);

namespace Modules\Marketing\Segments\Core\Jobs;

use App\Jobs\Job;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Modules\Marketing\Segments\Core\Models\Logs\SegmentsLog;
use Modules\Marketing\Segments\Core\Models\Subscriber;
use Modules\Marketing\Segments\Core\Models\SubscribersSegment;
use MongoDB\BSON\UTCDateTime;
use Throwable;

class SetSubscriberToSegmentLog extends Job
{
    /**
     * The name of the queue the job should be sent to.
     *
     * @var string|null
     */
    public $queue = 'segments1';



    /** @var null|SubscribersSegment $segment */
    protected $segment;


    /** @var Collection|Subscriber[] $subscribers */
    protected $subscribers;

    /**
     * SetSubscriberToSegmentLog constructor.
     */
    public function __construct(protected string $action, protected int $segmentId, protected array $subscribersIds)
    {
        $this->site_id = site('site_id');
        $this->subscribers = collect();
    }

    /**
     * {@inheritdoc}
     */
    public function execute()
    {
        if (!in_array($this->action, ['add', 'remove'])) {
            return static::EXECUTE_DESTROY;
        }

        $site = $this->getSite();
        if (!$site || $site->plan_expired) {
            $this->error2('Site is not exists or is expired');
            return $site ? static::SITE_PLAN_EXPIRED : static::MISSING_SITE;
        }

        if ($site->maintenance) {
            $this->warn('Site is in maintenance mode');
            return [static::SITE_MAINTENANCE];
        }

        //migrate job from one to other platform
        if (($platform = platform()) != ($sitePlatform = sitePlatform())) {
            $this->info(sprintf('Migrate JOB from platform %s to platform %s', $platform, $sitePlatform));
            return [static::WRONG_PLATFORM];
        }

        $this->segment = SubscribersSegment::find($this->segmentId);
        if (!$this->segment) {
            $this->warn('Segment not exists');
            return static::EXECUTE_DESTROY;
        }

        $this->subscribers = Subscriber::with('customer')
            ->whereRaw(sprintf('id In (%s)', implode(',', $this->subscribersIds)))
            ->get();

        try {
            if ($this->subscribers->isNotEmpty()) {
                if ($this->action == 'add') {
                    $this->addToLog();
                } else {
                    $this->removeToLog();
                }
            }
        } catch (Throwable) {
            //
        }

        return static::EXECUTE_DESTROY;
    }

    private function addToLog(): void
    {
        if ($this->subscribers->isNotEmpty()) {
            if ($this->subscribers->count() == 1) {
                /** @var Subscriber $subscriber */
                $subscriber = $this->subscribers->first();
                SegmentsLog::create([
                    'site_id' => (int)$this->site_id,
                    'segment_id' => $this->segment->id,
                    'segment_name' => $this->segment->name,
                    'segment_conditions' => $this->segment->conditions_formatted,
                    'segment_channel' => $this->segment->channel,
                    'created_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                    'updated_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                    'type' => 'single-row',
                    'subscriber_id' => $subscriber->id,
                    'customer_id' => optional($subscriber->customer)->id,
                    'first_name' => $subscriber->first_name,
                    'last_name' => $subscriber->last_name,
                    'country' => $subscriber->country,
                    'action' => 'added_to_segment',
                ]);
            } else {
                $segment = [
                    'site_id' => (int)$this->site_id,
                    'segment_id' => $this->segment->id,
                    'segment_name' => $this->segment->name,
                    'segment_conditions' => $this->segment->conditions_formatted,
                    'segment_channel' => $this->segment->channel,
                    'created_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                    'updated_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                    'type' => 'row',
                    'log_group' => 'segments',
                ];

                $log = SegmentsLog::create(array_merge($segment, [
                    'action' => 'begin_added_to_segment',
                    'type' => 'head',
                ]));

                $subscribers = $this->subscribers->map(fn(Subscriber $subscriber): array => array_merge($segment, [
                    'parent_id' => $log->id,
                    'subscriber_id' => $subscriber->id,
                    'customer_id' => optional($subscriber->customer)->id,
                    'first_name' => $subscriber->first_name,
                    'last_name' => $subscriber->last_name,
                    'country' => $subscriber->country,
                    'action' => 'added_to_segment',
                ]));

                SegmentsLog::insert($subscribers->all());
            }
        }
    }

    private function removeToLog(): void
    {
        if ($this->subscribers->isNotEmpty()) {
            if ($this->subscribers->count() == 1) {
                /** @var Subscriber $subscriber */
                $subscriber = $this->subscribers->first();
                SegmentsLog::create([
                    'site_id' => (int)$this->site_id,
                    'segment_id' => $this->segment->id,
                    'segment_name' => $this->segment->name,
                    'segment_conditions' => $this->segment->conditions_formatted,
                    'segment_channel' => $this->segment->channel,
                    'created_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                    'updated_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                    'type' => 'single-row',
                    'subscriber_id' => $subscriber->id,
                    'customer_id' => optional($subscriber->customer)->id,
                    'first_name' => $subscriber->first_name,
                    'last_name' => $subscriber->last_name,
                    'country' => $subscriber->country,
                    'action' => 'removed_from_segment',
                ]);
            } else {
                $segment = [
                    'site_id' => (int)$this->site_id,
                    'segment_id' => $this->segment->id,
                    'segment_name' => $this->segment->name,
                    'segment_conditions' => $this->segment->conditions_formatted,
                    'segment_channel' => $this->segment->channel,
                    'created_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                    'updated_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                    'type' => 'row',
                    'log_group' => 'segments',
                ];

                $log = SegmentsLog::create(array_merge($segment, [
                    'action' => 'begin_removed_from_segment',
                    'type' => 'head',
                ]));

                $subscribers = $this->subscribers->map(fn(Subscriber $subscriber): array => array_merge($segment, [
                    'parent_id' => $log->id,
                    'subscriber_id' => $subscriber->id,
                    'customer_id' => optional($subscriber->customer)->id,
                    'first_name' => $subscriber->first_name,
                    'last_name' => $subscriber->last_name,
                    'country' => $subscriber->country,
                    'action' => 'removed_from_segment',
                ]));

                SegmentsLog::insert($subscribers->all());
            }
        }
    }
}
