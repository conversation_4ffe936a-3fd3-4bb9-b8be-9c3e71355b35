<?php

declare(strict_types=1);

namespace Modules\Marketing\Segments\Core\Listeners;

use App\Integration\Facebook\Models\MessengerSubscriber;
use Modules\Marketing\Segments\Core\Models\SubscriberChannel;

class MessengerSubscriberListener
{
    /**
     * @param \App\Integration\Facebook\Models\MessengerSubscriber $subscriber
     * @return mixed
     */
    public static function onDeletedMessengerSubscriber(MessengerSubscriber $subscriber): void
    {
        SubscriberChannel::where('channel', 'Messenger')
            ->where('channel_identifier', $subscriber->psid)
            ->delete();
    }
}
