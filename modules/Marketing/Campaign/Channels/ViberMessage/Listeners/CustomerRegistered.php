<?php

declare(strict_types=1);

namespace Modules\Marketing\Campaign\Channels\ViberMessage\Listeners;

use App\Models\Customer\Customer;
use App\Models\Queue\SiteQueue;
use Illuminate\Auth\Events\Registered;
use Throwable;

/**
 * Class CustomerRegistered
 * @package Modules\Marketing\Campaign\Channels\ViberMessage\Listeners
 */
class CustomerRegistered extends AbstractListener
{
    /**
     * @param Registered $registered
     */
    public function handle(Registered $registered): void
    {
        if (!($registered->user instanceof Customer)) {
            return;
        }

        if (!$this->allowSendMessage()) {
            return;
        }

        try {
            SiteQueue::executeQueueTask('viber_message_customer_registered', [
                'customer_id' => $registered->user->id
            ], null, 60);
        } catch (Throwable) {
        }
    }
}
