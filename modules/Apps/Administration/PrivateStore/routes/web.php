<?php

declare(strict_types=1);

use Illuminate\Routing\Router;

Route::group(['prefix' => 'api/private-store', 'namespace' => 'SiteCp', 'middleware' => 'hasApiPermission:apps'], function (Router $router): void {
    $router->get('settings', ['as' => 'api.private-store.settings', 'uses' => 'AppController@settings']);
    $router->post('settings', ['uses' => 'AppController@settingsSaveCustom']);
    $router->post('install', ['as' => 'apps.private-store.install', 'uses' => 'AppController@install']);
    $router->post('uninstall', ['as' => 'apps.private-store.uninstall', 'uses' => 'AppController@uninstall']);
});
