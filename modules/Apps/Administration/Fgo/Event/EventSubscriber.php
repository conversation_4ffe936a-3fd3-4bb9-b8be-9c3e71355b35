<?php

declare(strict_types=1);

namespace Modules\Apps\Administration\Fgo\Event;

use App\Events\FulfillmentAdd;
use App\Events\OrderCreated;
use App\Events\OrderStatusChange;
use Illuminate\Events\Dispatcher;
use Modules\Apps\Administration\Fgo\FgoManager;

/**
 * Class OrderEventSubscriber
 *
 * @package App\Listeners
 */
class EventSubscriber
{
    /**
     * @param $event
     * @return null
     * @throws \App\Exceptions\Error
     */
    public function onStatusChange($event): void
    {
        $manager = new FgoManager();
        if ($manager->isActive() && $manager->getSetting('automate_generate', 0) == 1) {
            $generate_status = $manager->getSetting('order_status');
            $order = null;
            if ($event instanceof OrderCreated && ($event->order->status == 'new_order')) {
                $order = $event->order;
            }

            if ($event instanceof OrderStatusChange && $event->order->status == 'completed' && $generate_status == 'completed') {
                $order = $event->order;
            }

            if ($event instanceof OrderStatusChange && $event->order->status == 'paid' && $generate_status == 'paid') {
                $order = $event->order;
            }

            if ($event instanceof FulfillmentAdd && $event->fulfillment->order->status == 'fulfilled') {
                $order = $event->fulfillment->order;
            }

            if ($order) {
                if ($manager->getSetting('is_billing_address', 0) == 1 && !empty($order->billingAddress)) {
                    $manager->generateDocument($order);
                }

                if ($manager->getSetting('is_billing_address', 0) == 0) {
                    $manager->generateDocument($order);
                }
            }
        }

        return;
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @param Dispatcher $events
     */
    public function subscribe($events): void
    {
        $events->listen(
            [
                \App\Events\OrderCreated::class,
                \App\Events\OrderStatusChange::class,
                \App\Events\FulfillmentAdd::class,
            ],
            $this->onStatusChange(...)
        );
    }
}
