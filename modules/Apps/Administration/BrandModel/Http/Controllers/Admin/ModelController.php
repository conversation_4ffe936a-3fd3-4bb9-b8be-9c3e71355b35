<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 18.7.2019 г.
 * Time: 09:14 ч.
 */

namespace Modules\Apps\Administration\BrandModel\Http\Controllers\Admin;

use App\Exceptions\Error;
use App\Traits\IController;
use Illuminate\Http\JsonResponse;
use Mo<PERSON>les\Apps\Abstractions\Controllers\AbstractAppsController;
use Modules\Apps\Administration\BrandModel\BrandModelManager;
use Modules\Apps\Administration\BrandModel\Filters\ModelFilter;
use Modules\Apps\Administration\BrandModel\Http\Requests\ChangeStatusRequest;
use Modules\Apps\Administration\BrandModel\Http\Requests\ModelRequest;
use Modules\Apps\Administration\BrandModel\Models\Brand;
use Modules\Apps\Administration\BrandModel\Models\Model;
use Modules\Core\Core\Traits\GridFilters;

class ModelController extends AbstractAppsController
{
    use IController;
    use GridFilters;

    /**
     * @var null|Brand
     */
    protected $brand;

    /**
     * AppController constructor.
     * @param BrandModelManager $manager
     * @throws \App\Exceptions\Error
     */
    public function __construct(BrandModelManager $manager)
    {
        parent::__construct($manager);
        $this->middleware('cc_apps_installed:' . $this->manager->getKey())->except('install');
    }

    /**
     * @param $brand_id
     * @return \Illuminate\Http\JsonResponse
     * @throws \App\Exceptions\Error
     */
    public function index($brand_id)
    {
        $this->brand = Brand::findOrFail($brand_id);
        $model = $this->brand->models()->withCount(['to_products as products_count' => function ($query): void {
            $query->select(\Illuminate\Support\Facades\DB::raw('COUNT(DISTINCT product_id)'));
        }]);

        $records = $this->getRecords($model, ModelFilter::class);
        $records->each(function ($record): void {
            $record->image = $record->hasImage() ? $record->getImage('150x150') : null;
        });

        $records->setMeta('brand', [
            'brand' => array_merge($this->brand->toArray(), [
                'image' => $this->brand->hasImage() ? $this->brand->getImage('150x150') : null,
            ]),
        ]);

        return response()->json($records);
    }

    /**
     * @param ModelRequest $request
     * @param $brand_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(ModelRequest $request, $brand_id)
    {
        /** @var Brand $brand */
        $brand = Brand::findOrFail($brand_id);

        try {
            /** @var Model $model */
            $model = \Illuminate\Support\Facades\DB::transaction(fn() => $brand->models()->create($request->input()));

            $model->uploadImage();

            return response()->json([
                'model' => array_merge($model->toArray(), [
                    'image' => $model->hasImage() ? $model->getImage('150x150') : null,
                ]),
                'brand' => array_merge($brand->toArray(), [
                    'image' => $brand->hasImage() ? $brand->getImage('150x150') : null,
                ]),
                'site_name' => \LinkerCp::siteFullLink('/brand/' . $brand->url_handle . '/'),
            ]);
        } catch (\Throwable $throwable) {
            return response()->json([], $throwable->getCode());
        }
    }

    /**
     * @param $brand_id
     * @param $model_id
     * @return JsonResponse
     * @throws Error
     */
    public function get($brand_id, $model_id)
    {
        /** @var Brand $brand */
        $brand = Brand::findOrFail($brand_id);

        $model = $brand->models()->findOrFail($model_id);

        return response()->json([
            'model' => array_merge($model->toArray(), [
                'image' => $model->hasImage() ? $model->getImage('150x150') : null,
            ]),
            'brand' => array_merge($brand->toArray(), [
                'image' => $brand->hasImage() ? $brand->getImage('150x150') : null,
            ]),
            'site_name' => \LinkerCp::siteFullLink('/brand/' . $brand->url_handle . '/'),
        ]);
    }

    /**
     * @param ModelRequest $request
     * @param $brand_id
     * @param $model_id
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Foundation\Application|\Illuminate\Http\JsonResponse|\Illuminate\Http\Response
     */
    public function update(ModelRequest $request, $brand_id, $model_id)
    {
        /** @var Brand $brand */
        $brand = Brand::findOrFail($brand_id);

        $model = $brand->models()->findOrFail($model_id);

        try {
            \Illuminate\Support\Facades\DB::transaction(function () use ($model, $request): void {
                $model->update($request->input());
            });

            $model->uploadImage();

            return response()->json([
                'model' => array_merge($model->toArray(), [
                    'image' => $model->hasImage() ? $model->getImage('150x150') : null,
                ]),
                'brand' => array_merge($brand->toArray(), [
                    'image' => $brand->hasImage() ? $brand->getImage('150x150') : null,
                ]),
                'site_name' => \LinkerCp::siteFullLink('/brand/' . $brand->url_handle . '/'),
            ]);
        } catch (\Throwable $throwable) {
            return response([], $throwable->getCode());
        }
    }

    /**
     * @param ChangeStatusRequest $request
     * @param $brand_id
     * @param $id
     * @return JsonResponse
     */
    public function status(ChangeStatusRequest $request, $brand_id, $id): JsonResponse
    {
        Model::findOrFail($id)
            ->update([
                'active' => intval($request->input('status'))
            ]);

        return response()->json([]);
    }
}
