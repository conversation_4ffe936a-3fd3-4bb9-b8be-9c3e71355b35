<?php

declare(strict_types=1);

namespace Modules\Apps\Administration\StoreLocations\Middleware;

use App\Helper\ArrayCache;
use App\Models\Page\Page;
use Closure;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\URL;
use Modules\Apps\Administration\StoreLocations\Models\ZoneStores;

class IsCookie
{
    protected $routesIgnore = [
        '.well-known/acme-challenge/*',
        'auth/*',
        'account',
        'cart/compact',
        'cart/compact/*',
        'api/v1/*',
        'xml_feed/*',
        'app/feed/*',
        'apps/microinvest/*',
        'blog/*',
        'contacts',
    ];

    protected $routes = [
        'product/*',
        'vendors',
        'vendor/*',
        'search',
        'category/*',
        'categories',
        'selection/*',
        'uptime',
        '/',
    ];

    /**
     * @param \Illuminate\Http\Request $request
     * @param Closure $next
     * @return RedirectResponse|mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (
            app_namespace() == 'site' &&
            !\Cookie::has('store_location') &&
            !$request->ajax() &&
            \Apps::installed('store_locations') &&
            ArrayCache::remember('ZoneStores.exists', fn() => ZoneStores::exists()) &&
            \Apps::setting('store_locations', 'access_to_site', 0) == 1 &&
            \Crawler::isCrawler()
        ) {
            $address['geo_zone_id'] = \Apps::setting('store_locations', 'default_shop', 0);
            $address['no_address_exist'] = 1;
            $cookie = \Cookie::make('store_location', json_encode($address), \Apps::setting('store_locations', 'cookie_live', 10080));
            \Cookie::queue($cookie);
        }

        if (
            app_namespace() == 'site' &&
            !\Cookie::has('store_location') &&
            !$request->ajax() &&
            !\Crawler::isCrawler() &&
            \Apps::installed('store_locations') &&
            ArrayCache::remember('ZoneStores.exists', fn() => ZoneStores::exists())
            && $this->checkIgnoreUrl($request)
        ) {
            if (\Apps::setting('store_locations', 'access_to_site', 0) == 1) {
                $address['geo_zone_id'] = \Apps::setting('store_locations', 'default_shop', 0);
                $address['no_address_exist'] = 1;
                $cookie = \Cookie::make('store_location', json_encode($address), \Apps::setting('store_locations', 'cookie_live', 10080));
                \Cookie::queue($cookie);
            } else {
                $pageId = \Apps::setting('store_locations', 'redirect_to');
                \Cookie::queue(\Cookie::make('store_location_refferal', URL::current(), 10080));
                if ($pageId && ($page = Page::find($pageId)) && !$request->is('page/' . $page->url_handle)) {
                    return redirect($page->url());
                }
            }
        }

        return $next($request);
    }

    /**
     * @param Request $request
     * @return bool
     */
    protected function checkIgnoreUrl(Request $request)
    {
        //  dd($request->is($this->routes));
        return $request->is($this->routes);
    }
}
