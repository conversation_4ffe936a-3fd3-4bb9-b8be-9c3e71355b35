<?php

declare(strict_types=1);

return [
    'header.install' => 'Install Multilingual Store',
    'info.title' => 'Multilingual Store',
    'info.create' => 'Create',
    'help.install' => 'With CloudCart, you can have your online store up and running in multiple languages in minutes, without any headaches.',
    'input.domain.label' => 'Choose a domain for the new language version',
    'button.check.domain' => 'Domain Verification',
    'domain.required' => 'You have not selected a domain for the new language version',
    'language.required' => 'You have not selected a default language for the new language version',
    'currency.required' => 'You have not selected a currency for the new language version',
    'error.domain_exists' => 'Selected domain is busy',
    'button.next_step' => 'Next Step',
    'input.rounding.label' => 'Rounding',
    'input.rounding.help' => 'After conversion, the amount will be rounded to the value you selected',
    'step.1.title' => 'Domain and Language Selection',
    'step.1.title.info' => 'Choose a domain, language and currency for the new language version',
    'step.2.title' => 'Select items for initial copy',
    'step.2.title.info' => 'Select items to copy in the new language version',
    'step.2.title.progress' => 'Select items to be copied in the language version',
    'step.1.complete' => 'A new language version process has been started for your website',
    'step.2.complete' => 'Settings saved successfully. You will be redirected to step 3',
    'step.copy.design' => 'Design',
    'step.copy.design.copy' => 'Copy design',
    'step.copy.menu' => 'Menu',
    'step.copy.menu.copy' => 'Copy menu',
    'step.copy.menu.translate' => 'Artificial Intelligence Translation',
    'step.copy.blog' => 'Blog',
    'step.copy.blog.copy' => 'Copy blog (categories and articles)',
    'step.copy.blog.translate' => 'Artificial Intelligence Translation',
    'step.copy.client' => 'Clients',
    'step.copy.client.copy' => 'Copy',
    'step.copy.shippings_payments' => 'Shipping & Payments',
    'step.copy.shippings_payments.shippings_copy' => 'Copy',
    'step.copy.shippings_payments.payments_copy' => 'Copy',
    'step.copy.product_reviews' => 'Product Reviews',
    'step.copy.product_reviews.copy' => 'Copy',
    'step.description.design' => 'By selecting this option, we will copy from the '.'<b> %s </b>'.' to the '.'<b> %s </b>'.', the Template Design, its settings, also if you have your own code.',
    'step.description.menu' => 'By selecting this option, we will copy from the '.'<b> %s </b>'.' to the '.'<b> %s </b>'.', all menus that were created in the original online store. You will also be able to select the "Artificial Intelligence Translation" option on each menu item. ',
    'step.description.blog' => 'By selecting this option, we will copy from the '.'<b> %s </b>'.' to the '.'<b> %s </b>'.', all blog categories and all articles related to them. You will also have the option to choose the "Artificial Intelligence Translation" option. ',
    'step.3.title' => 'Copy Product Catalog - Product Details',
    'step.3.title.info' => 'Select product details to be copied to the new language version',
    'step.3.title.progress' => 'Select product details to be copied in the language version',
    'step.copy.name' => 'Product Name',
    'step.copy.name.copy' => 'Copy product name',
    'step.copy.name.translate' => 'Artificial Intelligence Translation',
    'step.copy.description' => 'Full description',
    'step.copy.description.copy' => 'Copy description',
    'step.copy.description.translate' => 'Artificial Intelligence Translation',
    'step.copy.short_description' => 'Short description',
    'step.copy.short_description.copy' => 'Copy short description',
    'step.copy.short_description.translate' => 'Artificial Intelligence Translation',
    'step.copy.category' => 'Category',
    'step.copy.category.copy' => 'Copy category',
    'step.copy.category.translate' => 'Artificial Intelligence Translation',
    'step.copy.vendor' => 'Manufacturer',
    'step.copy.vendor.copy' => 'Copy vendor',
    'step.copy.variety' => 'Varieties',
    'step.copy.variety.copy' => 'Copy varieties and values',
    'step.copy.variety.translate' => 'Artificial Intelligence Translation',
    'step.copy.images' => 'Images',
    'step.copy.images.copy' => 'Copy images',
    'step.copy.price' => 'Price',
    'step.copy.price.copy' => 'Copy price',
    'step.copy.price.conversion' => 'Conversion',
    'step.copy.options' => 'Product Options',
    'step.copy.options.copy' => 'Copy',
    'step.copy.options.translate' => 'Artificial Intelligence Translation',
    'step.copy.meta_title' => 'Meta title',
    'step.copy.meta_title.translate' => 'Artificial Intelligence Translation',
    'step.copy.meta_description' => 'Meta description',
    'step.copy.meta_description.translate' => 'Artificial Intelligence Translation',
    'step.copy.images_alt' => 'Image Alt Tags',
    'step.copy.images_alt.translate' => 'Artificial Intelligence Translation',
    'step.copy.change.price' => 'Multiply the original price by',
    'step.3.complete' => 'Settings saved successfully. You will be redirected to step 4',
    'step.4.title' => 'Select Plan',
    'step.4.title.info' => 'Choose a plan for the new language version',
    'step.4.title.progress' => 'Choose plan and applications',
    'step.next.payment' => 'Continue to pay',
    'input.unit_system' => 'Unit of Measure',
    'input.unit_system.metric' => 'Kg.',
    'input.unit_system.imperial' => 'Pound',
    'site.status.initializing' => 'Preparing for installation',
    'site.status.installing' => 'Installation',
    'site.status.finalizing' => 'Configuration',
    'site.status.completed' => 'Configuration',
    'button.create' => 'Add language version',
    'table.created' => 'Creation date',
    'table.name' => 'Store Name',
    'table.url' => 'Address',
    'table.status' => 'Status',
    'table.loggin' => 'Login to Administration',
    'table.lang' => 'Language',
    'table.currency' => 'Currency',
    'step.copy.pages' => 'Pages',
    'step.copy.pages.copy' => 'Copy pages',
    'step.copy.pages.translate' => 'Artificial Intelligence Translation',
    'step.copy.product' => 'Products',
    'step.copy.product.copy' => 'Copy Products',
    'step.copy.round.price' => 'Set the decimals of the price to',
    'step.description.pages' => ' By selecting this option, we will copy from the '.'<b> %s </b>'.' to the '.'<b> %s </b>'.', all Landing Pages. You will also be able to select the "Artificial Intelligence Translation" option for all pages not created with the visual editor. ',
    'step.description.product' => 'If you select this option, all products will be copied from the'.' <b> %s </b>'.' to the '.'<b> %s </b>'.'. You will also be able to change the price of the products. You will also have the option to choose the "Artificial Intelligence Translation" option.',
    'a.change_plan' => 'Change plan',
    'step.5.title.progress' => 'View',
    'preview.header.service' => 'SERVICE',
    'preview.header.qty' => 'QUANTITY',
    'preview.header.price' => 'PRICE',
    'service.name.copy' => 'Copy Information',
    'service.desc.copy' => 'Copy information from main site',
    'service.name.translate' => 'Artificial Intelligence Translation',
    'preview.payment' => 'Payment amount:',
    'preview.payment.vat' => '* Prices are without VAT',
    'surcharge.msg' => 'The process of creating a new language version cannot continue. You have unpaid services. <br /> <b> Please pay or change your language settings! </b> ',
    'surcharge.button.edit' => 'Change settings',
    'progress.selected_plan' => 'Subscription plan upgrade',
    'progress.install_app' => 'Application Installation',
    'progress.copy_design' => 'Copy Design',
    'progress.copy_menu' => 'Copy Menu',
    'progress.copy_blog' => 'Copy Blog',
    'progress.copy_pages' => 'Copy Pages',
    'progress.copy_product' => 'Copy Products',
    'progress.machine_translation' => 'Artificial Intelligence Translation',
    'progress.copy_storage' => 'Copy files',
    'info.step_2' => 'Step 2',
    'info.step_2.title' => 'Step 2 - Select elements to be copied from ',
    'info.step_3' => 'Step 3',
    'info.step_3.title' => 'Step 3 - Select product elements to be copied from ',
    'step.copy.meta_title.copy' => 'Copy meta title',
    'step.copy.meta_description.copy' => 'Copy meta description',
    'step.copy.images_alt.copy' => 'Copy alt tags',
    'info.step_4' => 'Step 4',
    'info.step_4.title' => 'Step 4 - Choose a subscription plan for ',
    'info.step_5' => 'Step 5',
    'info.step_5.title' => 'Step 5 - Confirmation of your order',
    'info.step_6' => 'Tracking progress',
    'info.progress.finish' => 'Progress Progress',
    'info.progress.finish.help' => 'Track the progress of the new language version',
    'progress.copy_products' => 'Copy Products',
    'progress.update_products_price' => 'Update prices',
    'progress.status.text.complete' => 'Completed',
    'progress.status.text.in_progress' => 'Running',
    'button.products' => 'Approval products',
    'step.copy.round.price.tooltip' => 'This field allows you to create marketing prices for your products. <br /> Example: if the price of the product is '.site('currency').' 94.30 and enter a value of 95 in the field, the value of the product will change to 94.95',
    'step.4.complete' => 'You will be redirected to Payment',
    'site.status.configuration_complete' => 'Successfully installed and configured',
    'settings.title' => 'Settings',
    'settings.copy.products_%1$s_%1$s' => 'Copy newly added products from '.'<b> %1$s </b> to '.'<b> %2$s </b>',
    'settings.copy.price' => 'Copy price',
    'settings.method_copy' => 'Approval method',
    'settings.method_copy.manual' => 'Manual approval',
    'settings.method_copy.automatic' => 'Automatic approval',
    'settings.method_copy.example' => 'If the "Manual approval" option is selected, you will need to take action to approve the products to be copied from the main store to the respective store (language version). <br> With "Automatic approval" you will not need action from your side, and the system will create newly added products in the main store to the respective store (language version).',
    'settings.show_in_site' => 'Show language version on site',
    'settings.delete' => 'Automatically delete product',
    'table.products' => 'Products',
    'table.sites' => 'Sites',
    'bulk.remove.option' => 'Products successfully removed from the list!',
    'bulk.add.option' => 'Copy task started',
    'required.service.machnine_translate' => 'To use machine translation you need to purchase an additional service.',
    'required.service.copy_info' => 'To use copying products from the main site you need to purchase an additional service.',
    'setting.translate.title' => 'Translation of title',
    'setting.translate.description' => 'Translate description',
    'setting.translate.category' => 'Translate category',
    'setting.translate.variants' => 'Translation into varieties',
    'setting.translate.meta' => 'Translation of meta title and description',
    'setting.translate.alt' => 'Translation of alt tags',
    'configuration_msg' => 'To complete the process of creating a new language version, you need to configure the newly created sites.',
    'alert.configuration' => '<div class="alert alert-warning"><p>To complete the process of creating a new language version, you need to complete the configuration.</p></div>',
    'buy_text' => 'you need to purchase an additional ',
    'buy_service' => 'service',
    'button.configuration' => 'Complete the configuration',
    'text.success.configurations' => '<i class="fa fa-check"></i> Your store has been successfully configured.<hr /> You can return to the main store if you want to make additional settings for automatic copying and translation of newly added products.<br />',
    'text.success.configurations.return' => 'Return to main store',
    'sites.feature.copy.counter' => 'products',
    'sites.feature.copy.title' => 'Copy products in language versions',
    'sites.feature.copy.text_%1$s' => 'When you add a new product, it will be copied to other language versions according to your settings.<br />You have <b>%1$s</b> left to copy.',
    'sites.feature.translate.counter' => 'the translation symbol',
    'sites.feature.translate.title' => 'Translation of newly added products after copying',
    'sites.feature.translate.text_%1$s' => 'After copying the products, they will be automatically translated into other language versions according to your settings.<br />You have <b>%1$s</b> 0 credits.',
    'sites.more.feature' => ' Buy an additional package',
    'sites.feature.translate.error_%1$s' => 'You have not purchased a package to translate the copied products. You need to purchase an <a href="%1$s" target="_blank">additional package</a> so that newly created products can be translated after copying.',
    'sites.feature.copy.error_%1$s' => 'You have not purchased a package to copy the newly created products. You need to purchase an <a href="%1$s" target="_blank">additional package</a> so that newly created products can be copied.',
    'settings.approved_product' => 'Approve product copy',
    'show.in_site.label' => 'Show language versions on the site',
    'show.in_site.success' => 'The setting was successfully saved',
    'select.exist.store' => 'Link existing store',
    'select.new.store' => 'Create a new language version',
    'exist.title' => 'Select existing store',
    'exist.description' => 'The selected store will be displayed as an additional language version',
    'exist.select.label' => 'Select Store',
    'create.modal.title' => 'Create Language Version',
    'exist.create.success' => 'Selected store successfully added as new language version',
    'create.modal.description_%1$s_%1$s' => 'You have a choice of two options. If you already have an existing store that works as a language version, you can associate it with your main store and take advantage of the Multi-language app. To do this, select the "<a href="%1$s"><b>Connect existing store</b></a>"<br />If you want to create a completely new language version, select the "<a href="%2$s"><b>Create a new language version</b></a>" button',
    'bulk.approve' => 'Approve',
    'bulk.remove' => 'Remove',
    'domain.regex' => 'Use only letters, numbers and dashes.',
    'table.delete' => 'Remove language version',
    'table.delete.confirm' => 'Are you sure you want to remove the selected language version?',
    'delete.success' => 'You have successfully removed the selected language version',
    'step.subtotal' => 'Subtotal: ',
    'step.copy.tabs' => 'Product tabs',
    'step.copy.tabs.translate' => 'Artificial Intelligence Translation',
    'step.copy.tags' => 'Product tags',
    'step.copy.tags.translate' => 'Artificial Intelligence Translation',
    'bulk.add.products' => 'Add Products',
    'bulk.add.products.text' => 'Select products to be copied to the other language versions',
    'bulk.add.product.complete' => 'The selected products have been successfully prepared for copying to the other language versions',
    'setting.translate.product_tags' => 'Translate product tags',
    'setting.translate.tabs' => 'Translate product tabs',
    'setting.translate.properties' => 'Translate product properties',
    'setting.manipulate_url' => 'Manipulating URLs in descriptions',
    'setting.manipulate_url.1' => 'Remove the URLs from the description',
    'setting.manipulate_url.2' => 'Do not change the URLs at the description',
    'setting.manipulate_url.3' => 'Try to change the URLs with the new domain',
    'settings.remove_url' => 'If the system can not identify the right URL remove the link from the description',
    'step.copy.url_manipulation' => 'Manipulating URLs in descriptions',
    "error.site_not_found" => "The selected site does not exist",
    "settings.method_copy.automatic.msg" => "You will be automatically charged for each copy based on the selected settings!",
    "settings.method_copy.manual.msg" => "When adding new products, go to <a href='https://flxu2.ccdev.pro/admin/app/multilang/products-old' target='_blank'>Sync Information</a> to start copying new products.",

];
