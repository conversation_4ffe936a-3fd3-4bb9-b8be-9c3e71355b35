<div class="box-section">
    <div class="row">
        <div class="col-xs-2">
            <select name="option[]" class="form-control select2me row-col-select" data-no-input="true" data-autowidth="true">
                <option value="">{t}xml_sync.form_skeleton.select{/t}</option>
                {$options nofilter}
            </select>
        </div>

        <div class="col-xs-2">
            <select name="action[]" class="form-control select2me row-operation-select" data-no-input="true" data-autowidth="true">
                <option value="">{t}xml_sync.form_skeleton.select{/t}</option>
                {foreach from=$operations item=operation}
                    <option {if isset($o) && $o && $o->name == $operation->name}selected="selected"{/if} value="{$operation->name}" class="hide">{$operation->label}</option>
                {/foreach}
            </select>
        </div>
        
        <div class="col-xs-7">
            {foreach from=$operations item=operation}
                {include "./../operations/{$operation->name}.tpl"}
            {/foreach}
        </div>

        <div class="col-xs-1">
            <a href="#" class="fal fa-times-circle fa-lg cc-grey js-event-remove-row" style="margin-top: 8px;"></a>
        </div>
    </div>
</div>