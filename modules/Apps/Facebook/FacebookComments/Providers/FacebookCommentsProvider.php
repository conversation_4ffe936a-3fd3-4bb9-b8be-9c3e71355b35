<?php

declare(strict_types=1);

namespace Modules\Apps\Facebook\FacebookComments\Providers;


use App\Providers\RouteServiceProvider;
use Illuminate\Routing\Router;
use Illuminate\Support\ServiceProvider;
use Modules\Apps\Facebook\FacebookComments\Comment;

class FacebookCommentsProvider extends ServiceProvider
{


    protected $namespace = 'Modules\Apps\Facebook\FacebookComments\Http\Controllers';

    /**
     * @return void
     */
    #[\Override]
    public function register(): void
    {
        $this->app->singleton(Comment::APP_KEY, fn(): \Modules\Apps\Facebook\FacebookComments\Comment => new Comment());
    }

    /**
     * @return void
     * @throws \Exception
     */
    public function boot(): void
    {
        $this->registerRoutes();
    }

    /**
     * @return void
     * @throws \Exception
     */
    protected function registerRoutes()
    {
        $this->app->make('router')->group(['middleware' => RouteServiceProvider::siteCpMiddleware(), 'namespace' => $this->namespace], function (Router $route): void {
            $this->loadRoutesFrom(__DIR__ . '/../routes/sitecp.php');
        });
    }

    /**
     * @return void
     * @throws \Exception
     */
    protected function registerTranslations()
    {
        $this->loadTranslationsFrom(__DIR__ . '/../resources/lang/', Comment::APP_KEY);
    }

    /**
     * @return void
     * @throws \Exception
     */
    protected function registerViews()
    {
        $this->app->make('view')->addNamespace(Comment::APP_KEY, __DIR__ . '/../resources/views/');
    }
}
