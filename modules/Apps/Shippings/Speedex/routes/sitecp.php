<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 2.6.2017 г.
 * Time: 14:14 ч.
 */

//rapido
Route::group(['prefix' => 'shipping/speedex'], function (\Illuminate\Routing\Router $route): void {
    $route->get('/', ['as' => 'apps.speedex', 'uses' => 'SpeedexController@index']);
    $route->get('settings', ['as' => 'apps.speedex.settings', 'uses' => 'SpeedexController@settingsVue']);

    $route->get('/populate', ['as' => 'apps.speedex.populate', 'uses' => 'SpeedexController@populate']);
    $route->post('connect', ['as' => 'apps.speedex.connect', 'uses' => 'SpeedexController@connect']);
    $route->get('waybill/{order_id}', ['as' => 'apps.speedex.waybill', 'uses' => 'SpeedexController@waybill']);
    $route->post('waybill/{order_id}', ['as' => 'apps.speedex.waybill.save', 'uses' => 'SpeedexController@waybillSave']);
    $route->get('changePickup/{order_id}/{type}', ['as' => 'apps.speedex.changePickup', 'uses' => 'SpeedexController@changePickup']);
    $route->post('changePickup/{order_id}/{type}', ['uses' => 'SpeedexController@changePickupSave']);
    $route->get('pdf-select/{order_id}', ['as' => 'apps.speedex.print_waybill_select', 'uses' => 'SpeedexController@pdfSelect']);
    $route->get('pdf/{order_id}/{type}', ['as' => 'apps.speedex.print_waybill', 'uses' => 'SpeedexController@pdf']);
    $route->any('calculate/{order_id}', ['as' => 'apps.speedex.calculate', 'uses' => 'SpeedexController@calculate']);
    $route->any('price_list/{shopId}', ['as' => 'apps.speedex.price_list', 'uses' => 'SpeedexController@priceList']);
});

Route::group(['prefix' => 'api/speedex', 'middleware' => 'hasApiPermission:settings,store.shipping'], function (\Illuminate\Routing\Router $route): void {
    $route->post('install', ['as' => 'apps.speedex.install', 'uses' => 'SpeedexController@install']);
    $route->any('uninstall', ['as' => 'apps.speedex.uninstall', 'uses' => 'SpeedexController@uninstall']);
    $route->get('settings', ['as' => 'apps.api.speedex.settings', 'uses' => 'SpeedexController@settings']);
    $route->post('settings', ['uses' => 'SpeedexController@saveConfig']);
    $route->match(['get', 'post'], 'validate', ['as' => 'apps.api.speedex.validate', 'uses' => 'SpeedexController@isValidCredentials']);
    $route->post('logo', ['as' => 'apps.api.speedex.logo', 'uses' => 'SpeedexController@uploadLogo']);
    $route->get('services', ['as' => 'apps.api.speedex.services', 'uses' => 'SpeedexController@getServices']);

});
