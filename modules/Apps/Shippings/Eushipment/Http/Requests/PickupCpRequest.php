<?php

declare(strict_types=1);

namespace Modules\Apps\Shippings\Eushipment\Http\Requests;

use App\Http\Request\Sitecp\Addresses\AddressRequest;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;
use Modules\Apps\Shippings\Omniship\Traits\PickUpRequest;
use OmniShip;
use Omniship\Address\Office;

class PickupCpRequest extends FormRequest
{
    use PickUpRequest;

    protected $office;

    /**
     * @var AddressRequest $address_request
     */
    protected $address_request;

    public function rules()
    {

        if (routeParameter('type') == 'office') {
            $rules = [
                'office_id' => 'required|office_exists',
            ];
        } else {
            $rules = $this->address_request->rules();
        }


        return $rules;
    }

    #[\Override]
    public function attributes()
    {
        return $this->address_request->attributes();
    }

    #[\Override]
    public function messages()
    {
        return array_merge($this->address_request->messages(), [
            'office_id.required' => __('shipping.err.office.required'),
            'office_id.office_exists' => __('shipping.err.office.not_exists')
        ]);
    }


    //    public function withValidator(Validator $validator)
    //    {
    //        $validator->addExtension('validateofficeExists', function ($attribute, $value, $parameters, $validator) {
    //
    //        });
    //    }

    /**
     * @param $value
     * @return Office
     */
    private function getOffice($value)
    {
        if (!$this->office) {
            try {
                $this->office = OmniShip::get('eushipment')->getOfficeById($value);
            } catch (Exception) {
            }
        }

        return $this->office;
    }

}
