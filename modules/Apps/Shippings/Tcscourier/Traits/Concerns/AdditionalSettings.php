<?php

declare(strict_types=1);

namespace Modules\Apps\Shippings\Tcscourier\Traits\Concerns;

use Modules\Core\Helpers\VueJs\BoxSettings;
use Modules\Core\Helpers\VueJs\Fields\LineField;
use Modules\Core\Helpers\VueJs\Fields\NumberField;
use Modules\Core\Helpers\VueJs\Fields\SwitchField;
use Modules\Core\Helpers\VueJs\ListBoxesSettings;

trait AdditionalSettings
{
    /**
     * @inheritdoc
     */
    public function getAdditionalSettings(): ListBoxesSettings
    {
        return ListBoxesSettings::make(static::APP_KEY)
            ->setBox($this->boxOne());
    }

    /**
     * @return BoxSettings
     */
    public function boxOne(): BoxSettings
    {
        return BoxSettings::make('general_settings')->setFields([
            new LineField(),
            SwitchField::make('cd', 'Enable cash on delivery'),
            NumberField::make('default_weight', 'Default weight for one item')->setUnit('weight'),
        ]);
    }
}
