<?php

declare(strict_types=1);

namespace Modules\Apps\Shippings\Ultracep\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ConnectRequest extends FormRequest
{
    public function rules(): array
    {
        if ($this->isMethod('post')) {
            return [
                'ultracep.email' => 'required',
                'ultracep.password' => 'required',
            ];
        }

        return [];
    }

    #[\Override]
    public function messages()
    {
        return [
            'ultracep.email.required' => 'Email address is required',
            'ultracep.password.required' => 'Password is required',
        ];
    }

}
