<?php

return[
    "title" => "Analytics",
    "navigation" => [
        "analytics" => "Analytics Dashboard",
        "reports" => "Analytics"
    ],
    "text" => [
        "no_comparison" => "No comparison",
        "compare_previous_period" => "Compare: Previous period",
        "compare_previous_year" => "Compare: Previous year",
        "previous_period" => "Previous period",
        "over_value" => "Over {value}",
        "statuses_title" => "Order statuses that will be included in the analyses",
        "show_devices" => "Show devices",
        "show_boxes_sort" => "Show boxes sort",
        "export_details_alert" => "This report shows up to {total} results. To see all results, you can",
        "average" => [
            "above" => "For period {period} {title}: {value} where is {percent} above the average for {industry}",
            "below" => "For period {period} {title}: {value} where is {percent} below the average for {industry}"
        ],
        "statuses" => "Statuses: {statuses}"
    ],
    "error" => [
        "server" => [
            "We cannot generate statistics for the selected period, please reduce it."
        ]
    ],
    "alert" => [
        "status" => "Data is visualized according to the default statuses in Settings and cannot be changed → Paid, Completed, Pending, Authorized payment, Fulfilled"
    ],
    "total-sales" => [
        "title" => "Total Sales",
        "subTitle" => "Sales over time",
        "tooltip" => [
            "boxTip" => "Total amount sales plus taxes and shipping, depend on selected order statuses in Settings."
        ],
        "details" => [
            "table" => [
                "date" => "Date",
                "product_name" => "Name",
                "product_type" => "Type",
                "order" => "Order",
                "price" => "Price",
                "discount" => "Discounts",
                "quantity" => "Quantity",
                "total_sale" => "Total sale"
            ],
            "type" => [
                "product" => "Product",
                "shipping" => "Shipping",
                "fee" => "Fee",
                "discount" => "Discount",
                "vat" => "Vat"
            ]
        ],
        "chart" => [
            "tooltip" => [
                "label" => "{amount} from {count} order for {date}|{amount} from {count} orders for {date}"
            ]
        ]
    ],
    "online-store-sessions" => [
        "title" => "Total Visits",
        "subTitle" => "Visitors over time",
        "tooltip" => [
            "boxTip" => "Number of visits in your online store."
        ],
        "chart" => [
            "tooltip" => [
                "label" => "{count} visitor for {date}|{count} visitors for {date}"
            ]
        ]
    ],
    "abandoned-carts" => [
        "title" => "Abandoned carts rate",
        "subTitle" => "Abandoned carts over time",
        "tooltip" => [
            "boxTip" => "Total of abandoned carts of customers per device not reached to checkout."
        ],
        "chart" => [
            "tooltip" => [
                "label" => "{percent} - {count} abandoned cart from {cart} for {date}|{percent} - {count} abandoned carts from {cart} for {date}"
            ]
        ]
    ],
    "abandoned-checkout" => [
        "title" => "Abandoned checkouts rate",
        "subTitle" => "Abandoned checkouts over time",
        "tooltip" => [
            "boxTip" => "Total of abandoned carts of customers reached to checkout."
        ],
        "chart" => [
            "tooltip" => [
                "label" => "{percent} - {count} abandoned checkout from {cart} for {date}|{percent} - {count} abandoned checkouts from {cart} for {date}"
            ]
        ]
    ],
    "top-order-products-by-units-sold" => [
        "title" => "Products by units sold",
        "amount" => "Amount {value}",
        "units" => "Unit {value}|Units {value}",
        "tooltip" => [
            "boxTip" => "Products with the most individual units sold. The data is visualized according to the following statuses of orders - Paid, Completed, Pending, Authorized payment, Fulfilled.",
            "device" => "Orders: {total}"
        ],
        "viewMore" => [
            "table" => [
                "date" => "Date",
                "name" => "Name",
                "views" => "Views / Sessions",
                "orders" => "Orders",
                "quantity" => "Units",
                "amount" => "Amount",
                "conversion_rate" => "Conversion rate"
            ],
            "chart" => [
                "tooltip" => [
                    "label" => "{quantity} units from {count} order for {date}|{quantity} units from {count} orders for {date}"
                ]
            ]
        ],
        "details" => [
            "table" => [
                "product_name" => "Name",
                "views" => "Views / Sessions",
                "orders" => "Orders",
                "quantity" => "Units",
                "amount" => "Amount",
                "conversion_rate" => "Conversion rate"
            ]
        ]
    ],
    "top-order-products-by-sales" => [
        "title" => "Products by sales",
        "unit" => "Unit {value}|Units {value}",
        "amount" => "Amount {value}",
        "tooltip" => [
            "boxTip" => "Best-selling products by their total value from all orders. The data is visualized according to the following statuses of orders - Paid, Completed, Pending, Authorized payment, Fulfilled.",
            "device" => "Orders: {total}"
        ],
        "viewMore" => [
            "table" => [
                "date" => "Date",
                "views" => "Views / Sessions",
                "orders" => "Orders",
                "amount" => "Amount",
                "quantity" => "Units",
                "conversion_rate" => "Conversion rate"
            ],
            "chart" => [
                "tooltip" => [
                    "label" => "{amount} from {count} order for {date}|{amount} from {count} orders for {date}"
                ]
            ]
        ],
        "details" => [
            "table" => [
                "product_name" => "Name",
                "views" => "Views / Sessions",
                "orders" => "Orders",
                "quantity" => "Units",
                "amount" => "Amount",
                "conversion_rate" => "Conversion rate"
            ]
        ]
    ],
    "top-products-by-traffic" => [
        "title" => "Products by traffic",
        "unit" => "Unit {value}|Units {value}",
        "views" => "View {value}|Views {value}",
        "tooltip" => [
            "boxTip" => "Top products depend on visits in your online store.",
            "device" => "Visits: {total}"
        ],
        "viewMore" => [
            "table" => [
                "date" => "Date",
                "views" => "Views / Sessions",
                "orders" => "Orders",
                "amount" => "Amount",
                "quantity" => "Units",
                "conversion_rate" => "Conversion rate"
            ],
            "chart" => [
                "tooltip" => [
                    "label" => "{count} view for {date}|{count} views for {date}"
                ]
            ]
        ],
        "details" => [
            "table" => [
                "product_name" => "Name",
                "views" => "Views / Sessions",
                "orders" => "Orders",
                "quantity" => "Units",
                "amount" => "Amount",
                "conversion_rate" => "Conversion rate"
            ]
        ]
    ],
    "landing-pages" => [
        "title" => "Landing pages by visits",
        "views" => "View {value}|Views {value}",
        "tooltip" => [
            "boxTip" => "Top landing pages where visitors entered your online store.",
            "device" => "Visits: {total}"
        ],
        "viewMore" => [
            "table" => [
                "date" => "Date",
                "views" => "Views / Sessions"
            ],
            "chart" => [
                "tooltip" => [
                    "label" => "{count} view for {date}|{count} views for {date}"
                ]
            ]
        ],
        "details" => [
            "table" => [
                "page_name" => "Name",
                "views" => "Views / Sessions"
            ]
        ]
    ],
    "landing-pages-by-sales" => [
        "title" => "Landing pages by sales",
        "sales" => "Sale {value}|Sales {value}",
        "orders" => "Order {value}|Orders {value}",
        "tooltip" => [
            "boxTip" => "Top landing pages where visitors entered your online store and placed an order, depend on selected order statuses in Settings.",
            "device" => "Orders: {total}"
        ],
        "viewMore" => [
            "table" => [
                "date" => "Date",
                "sales" => "Orders",
                "amount" => "Amount"
            ],
            "chart" => [
                "tooltip" => [
                    "label" => "{amount} from {count} order for {date}|{amount} from {count} orders for {date}"
                ]
            ]
        ],
        "details" => [
            "table" => [
                "page_name" => "Name",
                "sales" => "Orders",
                "amount" => "Amount"
            ]
        ]
    ],
    "total-orders" => [
        "title" => "Total Orders",
        "subTitle" => "Total sales over time",
        "tooltip" => [
            "boxTip" => "Total number of orders, depend on selected order statuses in Settings."
        ],
        "details" => [
            "table" => [
                "date" => "Date",
                "order" => "Order",
                "subtotal" => "Sub total",
                "discounts" => "Discounts",
                "vat_amount" => "Vat",
                "tax_amount" => "Fees",
                "shipping" => "Shipping",
                "amount" => "Total"
            ]
        ],
        "chart" => [
            "tooltip" => [
                "label" => "{count} order for {date}|{count} orders for {date}"
            ]
        ]
    ],
    "cart-conversion-rate" => [
        "title" => "Conversion Rate",
        "subTitle" => "Conversion rate over time",
        "tooltip" => [
            "boxTip" => "Percentage of visits resulting of orders, depend on selected order statuses in Settings."
        ],
        "chart" => [
            "tooltip" => [
                "label" => "{percent} for {date}"
            ]
        ],
        "details" => [
            "table" => [
                "date" => "Date",
                "sessions" => "Visitors / Sessions",
                "cart" => "Added to cart",
                "checkout" => "Checkout",
                "orders" => "Purchase",
                "rate" => "Conversion rate"
            ]
        ]
    ],
    "total-customers" => [
        "title" => "Total Customers",
        "subTitle" => "Customers over time",
        "tooltip" => [
            "boxTip" => "Total unique customers, depend on selected order statuses in Settings. Customers made more than one order in your store VS customers made an order into a time frame."
        ],
        "text" => [
            "new" => "New",
            "returning" => "Returning"
        ],
        "header" => [
            "custom" => [
                "row1" => "New",
                "row2" => "Returning"
            ]
        ],
        "chart" => [
            "tooltip" => [
                "label" => "{count} {label} customer|{count} {label} customers"
            ]
        ]
    ],
    "average-order-value" => [
        "title" => "Average Order Value",
        "subTitle" => "Order value over time",
        "tooltip" => [
            "boxTip" => "Total value of all orders divided by total number of orders, depend on selected order statuses in Settings."
        ],
        "details" => [
            "table" => [
                "date" => "Period",
                "amount" => "Average amount",
                "discount_amount" => "Discount amount",
                "products_discount_amount" => "Products Discount amount",
                "tax_amount" => "Tax",
                "vat_amount" => "Vat",
                "shipping_amount" => "Shipping",
                "shipping_discount_amount" => "Shipping discount"
            ]
        ],
        "chart" => [
            "tooltip" => [
                "label" => "{amount} for {date} from {count} order|{amount} for {date} from {count} orders"
            ]
        ]
    ],
    "orders-social-source" => [
        "title" => "Sales by Source / Medium",
        "title_details" => "Sales by {details}",
        "title_viewMore" => "Sales by Source / Medium / Campaign",
        "orders" => "Order {value}|Orders {value}",
        "sales" => "Sale {value}|Sales {value}",
        "tooltip" => [
            "boxTip" => "Total sales of orders, grouped by the type of traffic source per device linked them to your online store, depend on selected order statuses in Settings.",
            "device" => "Orders: {total}"
        ],
        "details" => [
            "table" => [
                "page_name" => "Name",
                "orders" => "Orders",
                "amount" => "Amount",
                "views" => "Visitors / Sessions",
                "conversion_rate" => "Conversion rate"
            ]
        ],
        "viewMore" => [
            "table" => [
                "date" => "Date",
                "orders" => "Orders",
                "amount" => "Amount",
                "views" => "Visitors / Sessions",
                "conversion_rate" => "Conversion rate"
            ],
            "chart" => [
                "tooltip" => [
                    "label" => "{amount} from {count} order for {date}|{amount} from {count} orders for {date}"
                ]
            ]
        ]
    ],
    "sessions-by-social-source" => [
        "title" => "Traffic by Source / Medium",
        "title_details" => "Traffic by {details}",
        "title_viewMore" => "Traffic by Source / Medium / Campaign",
        "sessions" => "Session {value}|Sessions {value}",
        "tooltip" => [
            "boxTip" => "Total visits, grouped by the type of traffic source linked them to your online store.",
            "device" => "Visits: {total}"
        ],
        "details" => [
            "table" => [
                "page_name" => "Name",
                "sales" => "Sales",
                "amount" => "Amount",
                "views" => "Visitors / Sessions",
                "conversion_rate" => "Conversion rate"
            ]
        ],
        "viewMore" => [
            "table" => [
                "date" => "Date",
                "sales" => "Sales",
                "amount" => "Amount",
                "views" => "Visitors / Sessions",
                "conversion_rate" => "Conversion rate"
            ],
            "chart" => [
                "tooltip" => [
                    "label" => "{count} view for {date}|{count} views for {date}"
                ]
            ]
        ]
    ],
    "top-categories-by-sales" => [
        "title" => "Categories by sales",
        "unit" => "Unit {value}|Units {value}",
        "sales" => "Sale {value}|Sales {value}",
        "tooltip" => [
            "boxTip" => "Best-selling categories of the total value of the products as an amount. The data is visualized according to the following statuses of orders - Paid, Completed, Pending, Authorized payment, Fulfilled.",
            "device" => "Orders: {total}"
        ],
        "viewMore" => [
            "table" => [
                "date" => "Date",
                "name" => "Name",
                "views" => "Views / Sessions",
                "orders" => "Orders",
                "quantity" => "Units",
                "amount" => "Amount",
                "conversion_rate" => "Conversion rate"
            ],
            "chart" => [
                "tooltip" => [
                    "label" => "{amount} from {count} order for {date}|{amount} from {count} orders for {date}"
                ]
            ]
        ],
        "details" => [
            "table" => [
                "page_name" => "Name",
                "views" => "Views / Sessions",
                "orders" => "Orders",
                "quantity" => "Units",
                "amount" => "Amount",
                "conversion_rate" => "Conversion rate"
            ]
        ]
    ],
    "top-categories-by-traffic" => [
        "title" => "Categories by traffic",
        "views" => "View {value}|Views {value}",
        "tooltip" => [
            "boxTip" => "Top visits categories in your online store.",
            "device" => "Visits: {total}"
        ],
        "viewMore" => [
            "table" => [
                "date" => "Date",
                "name" => "Name",
                "views" => "Views / Sessions",
                "orders" => "Orders",
                "quantity" => "Units",
                "amount" => "Amount",
                "conversion_rate" => "Conversion rate"
            ],
            "chart" => [
                "tooltip" => [
                    "label" => "{count} view for {date}|{count} views for {date}"
                ]
            ]
        ],
        "details" => [
            "table" => [
                "page_name" => "Name",
                "views" => "Views / Sessions",
                "orders" => "Orders",
                "quantity" => "Units",
                "amount" => "Amount",
                "conversion_rate" => "Conversion rate"
            ]
        ]
    ],
    "top-brands-by-sales" => [
        "title" => "Manufacturers by sales",
        "unit" => "Unit {value}|Units {value}",
        "sales" => "Sale {value}|Sales {value}",
        "tooltip" => [
            "boxTip" => "Best-selling vendors of the total value of the products as an amount. The data is visualized according to the following statuses of orders - Paid, Completed, Pending, Authorized payment, Fulfilled.",
            "device" => "Orders: {total}"
        ],
        "viewMore" => [
            "table" => [
                "date" => "Date",
                "name" => "Name",
                "views" => "Views / Sessions",
                "orders" => "Orders",
                "quantity" => "Units",
                "amount" => "Amount",
                "conversion_rate" => "Conversion rate"
            ],
            "chart" => [
                "tooltip" => [
                    "label" => "{amount} from {count} order for {date}|{amount} from {count} orders for {date}"
                ]
            ]
        ],
        "details" => [
            "table" => [
                "page_name" => "Name",
                "views" => "Views / Sessions",
                "orders" => "Orders",
                "quantity" => "Units",
                "amount" => "Amount",
                "conversion_rate" => "Conversion rate"
            ]
        ]
    ],
    "top-brands-by-traffic" => [
        "title" => "Manufacturers by traffic",
        "views" => "View {value}|Views {value}",
        "tooltip" => [
            "boxTip" => "Top visits vendors in your online store.",
            "device" => "Visits: {total}"
        ],
        "viewMore" => [
            "table" => [
                "date" => "Date",
                "name" => "Name",
                "views" => "Views / Sessions",
                "orders" => "Orders",
                "quantity" => "Units",
                "amount" => "Amount",
                "conversion_rate" => "Conversion rate"
            ],
            "chart" => [
                "tooltip" => [
                    "label" => "{count} view for {date}|{count} views for {date}"
                ]
            ]
        ],
        "details" => [
            "table" => [
                "page_name" => "Name",
                "views" => "Views / Sessions",
                "orders" => "Orders",
                "quantity" => "Units",
                "amount" => "Amount",
                "conversion_rate" => "Conversion rate"
            ]
        ]
    ],
    "sessions-by-traffic-source" => [
        "title" => "Visits by traffic source (referral)",
        "sessions" => "Session {value}|Sessions {value}",
        "tooltip" => [
            "boxTip" => "Number of visits grouped by the type of traffic source.",
            "device" => "Visits: {total}"
        ],
        "groups" => [
            "unknown" => "Unknown",
            "payments" => "Payments",
            "news" => "News",
            "search" => "Search",
            "email" => "Email",
            "paid" => "Paid",
            "social" => "Social"
        ],
        "details" => [
            "table" => [
                "page_name" => "Name",
                "orders" => "Orders",
                "views" => "Views / Sessions",
                "amount" => "Amount",
                "conversion_rate" => "Conversion rate"
            ]
        ],
        "viewMore" => [
            "table" => [
                "date" => "Date",
                "orders" => "Orders",
                "views" => "Views / Sessions",
                "amount" => "Amount",
                "conversion_rate" => "Conversion rate"
            ],
            "chart" => [
                "tooltip" => [
                    "label" => "{count} view for {date}|{count} views for {date}"
                ]
            ]
        ]
    ],
    "sales-by-traffic-source" => [
        "title" => "Sales by traffic source (referral)",
        "sales" => "Sale {value}|Sales {value}",
        "orders" => "Order {value}|Orders {value}",
        "tooltip" => [
            "boxTip" => "Total amount of all orders grouped by the type of traffic source, depend on selected order statuses in Settings.",
            "device" => "Orders: {total}"
        ],
        "groups" => [
            "unknown" => "Unknown",
            "payments" => "Payments",
            "news" => "News",
            "search" => "Search",
            "email" => "Email",
            "paid" => "Paid",
            "social" => "Social"
        ],
        "details" => [
            "table" => [
                "page_name" => "Name",
                "orders" => "Orders",
                "views" => "Views / Sessions",
                "amount" => "Amount",
                "conversion_rate" => "Conversion rate"
            ]
        ],
        "viewMore" => [
            "table" => [
                "date" => "Date",
                "orders" => "Orders",
                "views" => "Views / Sessions",
                "amount" => "Amount",
                "conversion_rate" => "Conversion rate"
            ],
            "chart" => [
                "tooltip" => [
                    "label" => "{amount} from {count} order for {date}|{amount} from {count} orders for {date}"
                ]
            ]
        ]
    ],
    "sessions-by-country" => [
        "title" => "Visits by location",
        "title_details" => "Visits by location {details}",
        "sessions" => "Session {value}|Sessions {value}",
        "tooltip" => [
            "boxTip" => "Number of visits by location on your online store.",
            "device" => "Visits: {total}"
        ],
        "details" => [
            "table" => [
                "page_name" => "Name",
                "views" => "Views / Sessions"
            ]
        ]
    ],
    "orders-by-country" => [
        "title" => "Sales by location",
        "title_details" => "Sales by location {details}",
        "orders" => "Order {value}|Orders {value}",
        "sales" => "Sale {value}|Sales {value}",
        "tooltip" => [
            "boxTip" => "Total amount of all orders by location per device on your online store, depend on selected order statuses in Settings.",
            "device" => "Orders: {total}"
        ],
        "viewMore" => [
            "table" => [
                "date" => "Date",
                "orders" => "Orders",
                "amount" => "Amount"
            ],
            "chart" => [
                "tooltip" => [
                    "label" => "{amount} from {count} order for {date}|{amount} from {count} orders for {date}"
                ]
            ]
        ],
        "details" => [
            "table" => [
                "page_name" => "Name",
                "orders" => "Orders",
                "amount" => "Amount"
            ]
        ]
    ],
    "sessions-by-device" => [
        "title" => "Online store sessions by device type"
    ],
    "cart-conversion-funnel" => [
        "title" => "Conversion Funnel",
        "subTitle" => "Conversion funnel",
        "tooltip" => [
            "boxTip" => "Event sequence data for number of customers reached to the cart, percentage of customers reached to the initiated checkout, and percentage of customers created an order, depend on selected order statuses in Settings."
        ],
        "group" => [
            "desktop" => "Desktop",
            "mobile" => "Mobile",
            "total" => "Total"
        ],
        "label" => [
            "cart" => "Cart",
            "checkout" => "Initiated Checkout",
            "order" => "Orders"
        ]
    ],
    "customer-value" => [
        "title" => "Customer Value",
        "subTitle" => "Order value over time",
        "tooltip" => [
            "boxTip" => "Total value of all orders divided by unique customers, depend on selected order statuses in Settings."
        ],
        "chart" => [
            "tooltip" => [
                "label" => "{amount} for {date} from {count} order|{amount} for {date} from {count} orders"
            ]
        ]
    ],
    "percentage-of-orders" => [
        "title" => "Sales distribution",
        "title_labels" => "Sales distribution {from} - {to} +",
        "tooltip" => [
            "boxTip" => "Orders distribution aggregate in accumulated range, depend on selected order statuses in Settings."
        ],
        "chart" => [
            "tooltip" => [
                "label" => "{percent} - {orders} from {count} order|{percent} - {orders} from {count} orders"
            ]
        ]
    ],
    "top-order-product-discounts" => [
        "title" => "Product discounts",
        "uses" => "Uses {value}",
        "tooltip" => [
            "boxTip" => "Most frequently used discounts on products in the order. The data is visualized according to the following statuses of orders - Paid, Completed, Pending, Authorized payment, Fulfilled.",
            "device" => "Orders: {total}"
        ],
        "help" => [
            "label" => [
                "exists" => "View discount",
                "deleted" => "Deleted discount",
                "manual" => "Manually created discount"
            ]
        ],
        "discount_type" => [
            "global" => "Global discount",
            "discount_code" => "Discount code",
            "shipping" => "Free shipping"
        ],
        "viewMore" => [
            "table" => [
                "date" => "Date",
                "name" => "Name",
                "order" => "Order",
                "product_name" => "Product",
                "type" => "Discount type",
                "amount" => "Discount"
            ]
        ],
        "details" => [
            "table" => [
                "page_name" => "Name",
                "sales" => "Orders",
                "uses" => "Uses",
                "amount" => "Discount"
            ]
        ]
    ],
    "top-order-discounts" => [
        "title" => "Order discounts",
        "uses" => "Uses {value}",
        "tooltip" => [
            "boxTip" => "Most frequently used discounts to the total value of the order. The data is visualized according to the following statuses of orders - Paid, Completed, Pending, Authorized payment, Fulfilled.",
            "device" => "Orders: {total}"
        ],
        "help" => [
            "label" => [
                "exists" => "View discount",
                "deleted" => "Deleted discount",
                "manual" => "Manually created discount"
            ]
        ],
        "discount_type" => [
            "global" => "Global discount",
            "discount_code" => "Discount code",
            "shipping" => "Free shipping"
        ],
        "viewMore" => [
            "table" => [
                "date" => "Date",
                "name" => "Name",
                "order" => "Order",
                "uses" => "Uses",
                "type" => "Discount type",
                "code" => "Code",
                "amount" => "Discount"
            ]
        ],
        "details" => [
            "table" => [
                "page_name" => "Name",
                "sales" => "Sales",
                "amount" => "Discount"
            ]
        ]
    ]
];
