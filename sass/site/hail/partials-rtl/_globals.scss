html {
    height: 100%;
}

body {
    @extend %flexbox;
    @include flex-direction(column);
    position: relative;
    height: 100%;
    width: 100%;
    overflow-x: hidden;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    font-size: $base-font-size;

    &.page-home {
        .background-image {
            display: none;
        }
    }

    &.body-rtl {
        .pull-left { float: right !important; }
        .pull-right { float: left !important; }
        .text-left { text-align: right; }
        .text-right { text-align: left; }
        .bracketing { display: inline-block; direction: ltr; }
        .date { display: inline-block; direction: ltr; }
        .order-product-quantity,
        .order-product-total-price,
        .total-value,
        .weight,
        .discount-box,
        .address-list li:last-of-type .address-value,
        .address-list li:nth-of-type(4) .address-value,
        .order-payments td { direction: ltr; }
        .cart-total-price,
        .navcart-total,
        .phone-value,
        .postal-code-value,
        .order-details-total span { display: inline-block; direction: ltr; }
        .order-payments td { text-align: right; }
        .product-cart-subtotal span { display: inline-block; direction: ltr; }
        time { display: inline-block; direction: ltr; }
        td { text-align: right; }
        th { text-align: right; }
        td[align="left"] { text-align: right; }
        td[align="right"] { text-align: left; }

        .ltr {
            direction: ltr;
        }

        .price_compare {

            span, del {
                display: inline-block;
                direction: ltr;
                padding: 0 2px;
            }
        }
    }
}

a{
    text-decoration: none !important;
}

.site-wrapper {
    position: relative;
    @include flex(1 0 auto);
}

svg {
    pointer-events: none;
}

img {
    max-width: 100%;

    &.js-lazy-load {
        opacity: 0;
        -webkit-transition: opacity .3s ease-out;
        -moz-transition: opacity .3s ease-out;
        -ms-transition: opacity .3s ease-out;
        -o-transition: opacity .3s ease-out;
        transition: opacity .3s ease-out;
    }
}

.user-register-captcha-field {
    -webkit-transform: scale(0.87);
    -moz-transform: scale(0.87);
    -ms-transform: scale(0.87);
    -o-transform: scale(0.87);
    transform: scale(0.87);
    transform-origin: right 0;
}

.cl {
    &:after {
        content: '';
        display: block;
        clear: both;
    }
}

#price_range {
    display: inline-block;
    direction: ltr;
}

.faq {
    margin: 15px 0;

    ul {
        list-style-type: circle;
        padding: 0 20px;
        
        li {
            margin-bottom: 20px;
        }
    }
}

.textbox, .product-details-description, .blog-article-content, .widget-info-box, .html-content-container-js, .slide-html, .showcase-info-box .description { 
    table {
        width: 100%;
        margin: 20px 0;

        th,
        td {
            border-collapse: collapse;
            padding: 3px 5px;
        }
    }

    ul { 
        list-style-type: disc; 
        list-style-position: inside; 
    }
    ol { 
        list-style-type: decimal; 
        list-style-position: inside; 
    }
    ul ul, ol ul { 
        list-style-type: circle; 
        list-style-position: inside; 
        margin-right: 15px; 
    }
    ol ol, ul ol { 
        list-style-type: lower-latin; 
        list-style-position: inside; 
        margin-right: 15px; 
    }

    img {
        max-width: 100% !important;
        height: auto !important;
    }

    .aligncenter {
        display: block;
        max-width: 100%;
        margin: 0 auto;
    }

    .alignleft {
        float: left;
        margin: 5px 20px 10px 0;
    }

    .alignright {
        float: right;
        margin: 5px 0 10px 20px;
    }

    .text-aligncenter {
        text-align: center;
    }

    .text-alignleft {
        text-align: left;
    }

    .text-alignright {
        text-align: right;
    }

    .text-alignjustify {
        text-align: justify;
    }
}

.textbox-iframe {
    height: 0;
    padding-bottom: 56.25%;
    position: relative;

    iframe {
        border: 0;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
    }
}

.bootstrap-touchspin {
    input[type="text"] {
        text-align: center;
    }
}

@mixin stickCenterV {
    position: absolute;
    top: 50%;
    @include transform(translateY(-50%));
}
@mixin stickCenterH {
    position: absolute;
    left: 50%;
    @include transform(translateX(-50%));
}
@mixin stickCenter {
    position: absolute;
    left: 50%;
    top: 50%;
    @include transform(translate(-50%, -50%));
}
.stickCenterV {
    @include stickCenterV;
}
.stickCenterH {
    @include stickCenterH;
}
.stickCenter {
    @include stickCenter;
}
.icono-stroke {
    border: $U2 solid;
}
[class*="icono-"] {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    font-style: normal;
    color: $icons_maincolor;
    text-align: left;
    text-indent: -9999px;
    direction: ltr;
    &:before, &:after {
        content:'';
        pointer-events: none;
    }
}
.icono-indent {
    width: $U20;
    height: $U16;
    border-width: $U4 0 $U4 $U8;
    border-style: solid;
    border-color: transparent;
    box-shadow: 0 -1*$U2, 0 $U2, inset 0 $U2, inset 0 -1*$U2;
    margin: $U9 $U7;
    &:before {
        @extend .stickCenterV;
        left: -1*$U8;
        border: $U5 solid;
        border-top-color: transparent;
        border-bottom-color: transparent;
        border-right-width: 0;
    }
}
.icono-caretRight {
    width: $U12;
    height: $U20;
    margin: $U7 $U11;
    &:before, &:after {
        width: $U14;
        height: $U2;
        position: absolute;
        top: 0;
        bottom: 0;
        margin: auto 0;
        right: $U2;
        box-shadow: inset 0 0 0 $U32;
        transform-origin: right;
    }
    &:before {
        @include transform(rotate(45deg));
    }
    &:after {
        @include transform(rotate(-45deg));
    }
}
.icono-caretLeft {
    @extend .icono-caretRight;
    @include transform(rotate(180deg));
}
.icono-search {
    @include square($U22);
    @include border-radius(50%);
    @extend .icono-stroke;
    @include transform(rotate(45deg));
    margin: $U4 $U4 $U8 $U8;
    &:before{
        width: $U4;
        height: $U11;
        @extend .stickCenterH;
        box-shadow: inset 0 0 0 $U32;
        top: $U19;
        @include border-radius(0 0 $U1 $U1);
    }
}
.icono-cross, .icono-crossCircle, .product-listing-filter-remove, .blog-filter-remove, .product-cart-product-remove {
    @include transform(rotate(45deg));
}

.mobile-slide-toggle-wrap {
    @extend .cl;
}

.mobile-slide-toggle {
    float: right;
    display: none;
    text-decoration: none !important;
    line-height: calculate-font($title-font-size) + 33px;
}

.bracketing { display: inline-block; }

h1 {
    @media (max-width: $break-sm) {
        font-size: 24px;
    }
}
h2 {
    @media (max-width: $break-sm) {
        font-size: 22px;
    }
}
h3 {
    @media (max-width: $break-sm) {
        font-size: 18px;
    }
}
h4 {
    @media (max-width: $break-sm) {
        font-size: 16px;
    }
}

.title {
    margin: 20px 0;
    h1, h2, h3, h4, h5 {
        font-size: $title-font-size;
        font-weight: $normal;
        line-height: 1.1;
        margin: 0;
    }
}

.black-title {
    margin: 30px 0;

    h1, h2, h3, h4, h5 {
        line-height: 45px;
    }

    background: none !important;
    width: 100%;
    padding: 0;

    h1 {
        display: inline-block;
        font-size: 30px !important;
        padding: 0;
        margin-bottom: 10px;
    }
}

.btn-primary {
    background: #ffffff !important;

    &:hover {
        @include opacity(0.6);
    }
}

.alert-danger {
    margin-bottom: 0;
}

.modal-dialog {
    border-top: 1px solid;
    .modal-content {
        @include box-shadow(0px 0px 25px rgba(0,0,0,0.25));
        @include border-radius(0px);
        border-width: 0 1px 3px 1px;
        border-style: solid;
        &.continue-shopping {
            .btn {
                width: 100%;
                margin-top: 20px;
            }

            .message {
                padding-bottom: 20px;
                display: block;
                text-align: center;
            }
        }
    }
    .modal-body {
        @include box-shadow(none);
        position: relative;
        .close {
            position: absolute;
            top: 0;
            right: 0;
            width: 25px;
            height: 25px;
            display: block;
            z-index: 9999;
            @include opacity(1);
            &:after {
                content: "+";
                display: block;
                text-align: center;
                line-height: 25px;
                font-size: 30px;
                font-weight: 100;
                @include rotate(45deg);
            }
            &:hover {
                @include opacity(.8);
            }
        }
    }
    .modal-footer {
        border-top: none;
        .btn {
            width: 100%;
        }
    }
    &.confirm {
        .modal-body, .modal-header {
            text-align: center;
            border: none;
        }
    }
}

.modal {
    .static-page-text,
    .textbox {
        padding: 10px 20px;

        @media (max-width: $break-md) {
            padding: 0;
        }
    }
}

.modal-backdrop.in {
    @include opacity(0.7);
}

.navbar-header {
    .header-actions {
        display: inline-block;
        margin-top: 40px;
        .search-box {
            input {
                line-height: 40px;
                height: 41px;
                margin-left: 5px;
                padding: 0 10px;
                display: inline-block;
                width: 200px;
            }
            button {
                height: 41px;
                width: 40px;
                display: inline-block;
                margin-left: -1px;
                i {
                    margin-top: 5px;
                }
            }
        }
        @media (max-width: $break-sm) {
            .search-box {
                position: absolute;
                top: -46px;
                left: 0;
                margin-right: 244px;
            }
        }
        @media (max-width: 505px) {
            .search-box {
                margin-right: 98px;
                input {
                    width: 150px;
                }
            }
        }
    }
}

.breadcrumb {
    padding: 25px 0;
    margin: 0;
    background: transparent;
    width: 100%;
    text-align: center;
    font-size: 11px;

    + h1 {
        padding: 15px 0;
        margin: 0 0 20px;
        background: transparent;
        width: 100%;
        text-align: center;
        font-size: 30px;
    }

    li, > li+li, li.active {
        &:before {
            display: none;
            /*content: "o";*/
        }
        &:after {
            content: "/ ";
            padding: 0 5px;
            color: #ccc;
        }
    }
    li:last-of-type:after {
        content: "";
    }
}

.pagination {
    width: 100%;
    padding: 15px 0;
    text-align: center;
    li {
        a {
            border: none;
            float: none;
            margin: 0 5px;
            padding: 8px 12px;
            display: block;
        }
        &:first-of-type {
            float: left;
            a:hover {
                background-color: transparent;
            }
        }
        &:last-of-type {
            float: right;
            a:hover {
                background-color: transparent;
            }
        }
    }
}

.error-box {
    text-align: center;
    margin-top: 80px;
    font-size: 50px;

    .info-container {
        width: 641px;
        margin: 0 auto;

        .error-heading {
            width: 460px;
            height: 460px;
            border-radius: 50%;
            margin: 0 auto;
            position: relative;

            h1 {
                font-size: 200px;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                margin: 0;
                font-weight: bold;
            }
        }

        .error-title {
            font-size: 72px;
            margin-top: 50px;
            display: block;
            font-weight: bold;
        }

        .message {
            font-size: 30px;
            display: block;
            margin: 0px 0 190px 0;
            font-weight: bold;
        }
    }

    @media (max-width: $break-md){
        .info-container {
            width: 441px;

            .error-heading {
                width: 300px;
                height: 300px;
                margin: 0 auto;
                position: relative;

                h1 {
                    font-size: 100px;
                }
            }

            .error-title {
                font-size: 40px;
                margin-top: 50px;
            }

            .message {
                font-size: 25px;
                margin: 0px 0 100px 0;
            }
        }
    }
    @media (max-width: $break-sm){
        .info-container {
            width: 241px;

            .error-heading {
                width: 200px;
                height: 200px;

                h1 {
                    font-size: 60px;
                }
            }

            .error-title {
                font-size: 30px;
                margin-top: 50px;
            }

            .message {
                font-size: 22px;
                margin: 0px 0 50px 0;
            }
        }
    }
    .error-title {
        font-size: 50px;
    }
    @media (max-width: $break-md) {
        .error-title {
            font-size: 30px;
        }
    }
    @media (max-width: $break-sm) {
        .error-title {
            font-size: 20px;
        }
    }
    .message {
        display: block;
    }
    .floor {
        width: 100%;
        height: 150px;
        margin-bottom: -30px;
        margin-top: -30px;
    }
    @media (max-width: $break-md){
        .floor {
            height: 100px;
        }
    }
    @media (max-width: $break-sm){
        .floor {
            height: 70px;
        }
    }
    img {
        margin-top: 20px;
        width: 100%;
    }
}

.mobile-slide-toggle {
    display: none;
}

.rc-anchor-light {
    border: none;
    @include box-shadow(0);
    @include border-radius(0);
}

.bootstrap-touchspin {
    position: relative;
    display: table;
    border-collapse: separate;
    .input-group-btn-vertical {
        position: relative;
        white-space: nowrap;
        width: 1%;
        vertical-align: middle;
        display: table-cell;
        button {
            display: block;
            float: none;
            width: 100%;
            max-width: 100%;
            padding: 4.5px 10px;
            margin-left: -1px;
            position: relative;
            font-size: 7px;
            @include border-radius(0);
        }
    }
}

.microdata {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
    z-index: -1;
}

.container-search {
    margin-top: 20px;

    .search-section {
        .title {
            margin: 0 0 20px;
            @extend .cl;

            h1 {
                padding: 15px;
                display: inline-block;
            }

            a {
                padding: 15px;
                float: right;
            }
        }

        ul {
            margin-bottom: 0;

            li {
                padding: 15px 0;
                @extend %flexbox;
                @include flex-flow(row);
                @include align-items(center);
                @include justify-content(center);

                > a {
                    @include flex(1 1 auto);
                    @extend %flexbox;
                    @include flex-flow(row);
                    @include align-items(center);

                }

                .image-holder {
                    @include flex(1 1 0);
                    text-align: center;
                    max-width: 15%;
                    margin-right: 15px;
                    
                    img {
                        display: inline-block;
                        max-width: 100%;
                        max-height: 100%;
                    }
                }

                .actions-box {
                    @extend %flexbox;
                    @include flex-flow(column);
                    @include justify-content(space-between);

                    .search-result-price {
                        text-align: right;
                        font-size: 18px;
                        line-height: 40px;
                    }
                }

                //overflow: hidden;
                //padding: 15px 0;
                //
                //.image-holder {
                //    float: left;
                //    width: 15%;
                //    max-width: 68px;
                //    max-height: 68px;
                //
                //    img {
                //        max-height: 100%;
                //        max-width: 100%;
                //        //width: 68px;
                //        //height: 68px;
                //    }
                //}
                //
                //.info-holder {
                //    float:left;
                //    width: 45%;
                //}
                //
                //.actions-box {
                //    float:right;
                //    margin-top: 14px;
                //}
                //
                //&:last-child {
                //    border-bottom: 0;
                //}
            }
        }
    }
}

.input-group-btn:first-child > .btn,
.input-group-btn:first-child > .btn-group {
    //margin-right: -2px;
}

.html-content-header {
    font-size: 18px;
    text-align: center;
}

.help-block-error {
    display: block;
    color: #ff0000;
}

.extra-contact-form-js {
    margin-bottom: 30px;
}

.search-results-no-results {
    text-align: center;

    h2 {
        font-size: 48px;
        color: #333;
        font-weight: normal;
        margin: 100px 0 70px 0;
    }

    p {
        font-size: 18px;
        color: #666666;
        display: block;
        margin-bottom: 20px;
    }

    form {
        display: inline-block;
        text-align: center;
        margin-bottom: 200px;
    }

    .search-box {
        input {
            line-height: 40px;
            height: 41px;
            margin-left: 5px;
            padding: 0 10px;
            display: inline-block;
            width: 200px;
        }
        button {
            background: transparent;
            display: block;
            padding-top: 5px;
            width: 40px;
            border: 1px solid #ccc;
            border-left: 0;
            height: 41px;

            i {
                background-image: url('../../layouts/hail/images/search.png?1433143830');
                display: inline-block;
                height: 16px;
                width: 16px;
            }
        }
    }
}

#googleMap {
    height: 350px !important;
}

@media (min-width: $break-sm) {
    .modal-large {
        width: 960px;
    }
}

@media (max-width: $break-sm) {

    .breadcrumb {
        + h1 {
            font-size: 26px;
        }
    }
    .pagination {
        display: block;
        position: relative;
        padding-top: 40px;
        text-align: center;

        li {
            &.page {
                a {
                    margin: 0 3px;
                }
            }

            a,
            &:last-child > a,
            &:last-child > span {
                margin: 0;
            }
        }

        .first,
        .last {
            display: none;
        }

        .prev {
            position: absolute;
            top: 0;
            left: 0;
        }

        .next {
            position: absolute;
            top: 0;
            right: 0;
        }
    }

    .modal-dialog {
        width: 650px !important;
        margin: 10px auto !important;
    }

    .user-register-captcha-field {
        -webkit-transform: scale(0.76);
        -moz-transform: scale(0.76);
        -ms-transform: scale(0.76);
        -o-transform: scale(0.76);
        transform: scale(0.76);
        transform-origin: right 0;
    }
}

@media (max-width: 500px) {
    .pagination {
        .first , .last {
            display: none;
        }
    }

    .container-search {
        .search-section {
            ul {
                li {
                    .image-holder {
                        float:left;
                        width: 20%;

                        img {
                            width: 68px;
                            height: 68px;
                        }
                    }

                    .info-holder {
                        float:right;
                        width: 60%;
                    }

                    .actions-box {
                        width: 100%;
                        float:left;
                        margin-top: 14px;

                        .search-result-price {
                            display: block;
                            text-align: center;
                        }

                        .btn-primary {
                            margin-top: 10px;
                            width: 100%;
                        }
                    }

                    &:last-child {
                        border-bottom: 0;
                    }
                }
            }
        }
    }
}

@media (max-width: $break-ls) {
    .breadcrumb {
        + h1 {
            font-size: 22px;
        }
    }

    .pagination {
        padding-top: 0;

        .next, .prev {
            display: none;
        }
    }

    .html-content-container-js .blog-atricle-content {
        img {
            &[style*="float:"] {
                margin: 5px auto !important;
                float: none !important;
                display: block !important;
            }
        }
    }

    .tabs ul li {
        float: none;
    }
}

@media (max-width: $break-xs) {
    .modal-dialog {
        width: 290px !important;
        margin: 5px auto !important;
    }
}