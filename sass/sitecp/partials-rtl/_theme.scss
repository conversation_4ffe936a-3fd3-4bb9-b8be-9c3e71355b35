.margin-right-5 {
    margin-right: 5px;
}

.border-0 {
    border: none !important;   
}

.img-border {
    width: 150px;
    height: 150px;
    display: inline-block;
    background-size: cover;
    background-position: center;
    vertical-align: top;
    border-radius: 50%;
}

.total-box {
    background-color: #f8f8f8;
    border-top: 1px solid #dedede;
    border-bottom: 1px solid #dedede;
    padding: 30px;
    position: relative;
}

.center {
    text-align: center;
}

.dash-margin {
    margin-top: 50px;
    margin-bottom: 70px;
}

.dash-margin-bottom {
    margin-bottom: 50px;
}

.panel {
    border-radius: 0;
    box-shadow: none;
}

/**     
* END TOOLTIPS
*/


.item-active-shipping {
    background-color: #ddefc4;
    border-radius: 50%;
    color: #9ecd5c;
    font-size: 16px;
    font-weight: 700;
    width: 35px;
    height: 35px;
    line-height: 35px;
    display: inline-table;
}

.item-active-shipping:hover {
    color: #9ecd5c;
}

.item-inactive-shipping {
    background-color: #DFF5FD;
    border-radius: 50%;
    color: #7BBFEC;
    font-size: 16px;
    font-weight: 700;
    width: 35px;
    height: 35px;
    line-height: 35px;
    display: inline-table;
}

.item-inactive-shipping:hover {
    color: #7BBFEC;
}

.item-active-products {
    background-color: #ddefc4;
    border-radius: 50%;
    color: #9ecd5c;
    font-size: 11px;
    font-weight: 700;
    width: 35px;
    height: 35px;
    line-height: 35px;
    display: inline-table;    
}

.item-inactive-products {
    background-color: #fde1df;
    border-radius: 50%;
    color: #f25347;
    font-size: 11px;
    font-weight: 700;
    width: 35px;
    height: 35px;
    line-height: 35px;
    display: inline-table;    
}

/**     
* TAGS
*/
.label {
    border-radius: 0;
    color: #2a313b;
    
    border: 1px solid;
}

.label.label-default {
    border-color: #e8e8e8;
    background-color: #fff;
}

/**     
* END TAGS
*/

/**     
* PAGER
*/

.pager li a {
    color: #4ea1ce;
    
    border: none;
    border-radius: none;
}

.pager li:hover a {
    color: #506684;
}


/**     
* END PAGER
*/




/**     
* TITLES
*/

h1 {
    font-size: 34px;
    font-weight: 300;
}

h2 {
    font-size: 29px;
    font-weight: 300;
}

h3 {
    font-size: 23px;
    font-weight: 300;
}

.content-padding {
    padding: 90px 30px 30px 30px;
    &.bricks-content {
        margin: 0 auto;
        max-width: 1400px;
    }
}

.main-title {
    font-size: 26px;
    color: #26292f;
    font-weight: 300;
    margin-top: 30px;
    margin-bottom: 10px;

    &:first-letter {

    }
    
    &.collapsable {
        &:after {
            content: "";
            display: inline-block;
            margin-right: 10px;
            vertical-align: super;
            margin-bottom: -2px;
            width: 0; 
            height: 0; 
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #d5d5d5;
        }
    }

    h1, h2, h3, h4, h5, h6 {
        font-weight: 300;
    }
}

.main-box {
    background-color: #fff;
    border: 1px solid #dedede;
    padding: 20px;
    &.logo-upload {
        position: relative;
        .main-box-header {
            margin: 0;
            .control-label {
                margin-bottom: 0 !important;
            }
        }
        &:after {
            content: "";                              
            background-color: #dedede;  
            display: block;
            width: 1px;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            margin: auto;
        }
        .logos-holder {
            height: 150px;
            display: table;
            width: 100%;
        }
        .logos-holder-inner {
            display: table-cell;
            vertical-align: middle;
            padding: 0 20px;
        }
        .box-image-label {
            display: block;
            margin: 0 0 10px;
        }
        .drag-content {
            border: 3px solid transparent;
            padding: 10px;
            border-radius: 6px;
            body.dragover &.hovered {
                border: 3px dashed #dedede;
            }
            > i {
                display: block;
                margin: 0px auto 10px auto;
            }
            .info-message {
                color: #b6b6b6;
                font-size: 18px;
                font-weight: 300;
                line-height: 28px; 
            } 
            .logo-image-holder {
                margin: 0 auto;
            }
        }
        .js-general-upload {
            overflow: hidden;
        }
    }
    #fileuploadFavicon,
    #fileuploadLogo {
        overflow: hidden;
    }
    #faviconImg {
        display: none;

        &.uploaded {
            display: block;
            margin: 0 auto 10px;
        }
    }
    .main-box-header {
        background-color: #f7f7f7;
        margin-left: -20px;
        margin-right: -20px;
        padding: 15px 20px;
        border-bottom: 1px solid #dedede;
        p {
            display: inline-block;
            margin-left: 10px !important;  
        }                                   
        p, .help-block {
            color: #7c7c7c !important;
            font-size: 14px !important;
            font-weight: 300 !important;
            margin: 0;
            vertical-align: middle;
        }
        .product-configuration-select {
            vertical-align: middle;
        }
    }
    .main-box-section {
        padding: 20px 0;
        position: relative;
        &:before {
            content: "";
            background-color: #dedede; 
            display: block;
            height: 1px;
            position: absolute;
            bottom: -1px;  
            left: -20px; 
            right: -20px;
        }     
        &:last-of-type {
            padding-bottom: 0; 
            &:before {
                display: none; 
            }
        }
        .remove {
            position: absolute;
            bottom: -25px;
            right: 0px;
        }
        .form-group {
            padding: 0 15px;   
        }
        &.overlay {
            position: relative;
            &:after {
                content: "";
                height: 99%;
                position: absolute;
                display: block;
                left: -20px;
                right: -20px;
                bottom: 0;
                z-index: 8;
                background-color: rgba(255, 255, 255, 0.80);
            }
        }
        &.carousel-position-box {
            padding: 20px 0;
            position: relative;
            &:before {
                content: "";
                background-color: #dedede;
                display: block;
                height: 1px;
                position: absolute;
                bottom: 0px;
                left: -20px;
                right: -20px;
            }
        }
    }
}

.main-box-head-image {
    margin: 0 -20px;
    border-bottom: 1px solid #dedede;

    img {
        width: 100%;
        height: auto;
    }
}

.main-box-no-title {
    margin-top: 30px;
}

.banner-holder {
    margin: 0 27px;
}

.slide-icon {
    display: inline-block;
    width: 40px;
    height: 40px;
    vertical-align: middle;
    background-color: #e7cb72;
    border: 1px solid #d8bc61;
    position: relative;
    margin-left: 10px;
    border-radius: 50%;
    i {
        position: absolute;
        display: block;
        margin: auto;
        left: 0;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
    }
}

.slide-choose-image-holder {
    .all-icons-add-link { margin: 0 0 0 5px !important; }
}

table.listing {
    border: 1px solid #dedede;
    margin-bottom: 0 !important;
    thead tr {
        border-bottom: 3px solid #dedede;
    }
    tbody > tr {
        &:nth-child(2n) {
            background-color: #fafafa;
        }
        &:hover {
            td {
                background-color: #fffbe9 !important;
            }
        }
    }
}

.table-footer {
    display: block;
    background-color: $secondary-bg-color;
    border: 1px solid #dedede;
    line-height: 68px;
    margin-top: -1px;
    padding: 0 20px;
}

/**     
* Forms
*/

.has-error .form-control:focus, div.form-group.has-error input.form-control {
    border-color: #e9ada8;
    box-shadow: none;
}

.form .labels {
    padding: 10px 0;
}

.form .form-group {
    padding: 30px 0 0 0;
    margin-bottom: 0;
}

.form.form-provider {
    border-top: 1px solid #ebebeb;
    padding-top: 20px;    
}

.form .left-border {
    border-left: 1px solid #ebebeb;
}

.form .right-border {
    border-right: 1px solid #ebebeb;
}

.form .form-group:last-child {
    border: 0;    
}

div.tagsinput {
    background-color: #f7f7f7;
    border-color: #e8e8e8;
}

label {
    font-size: 17px;
    font-weight: 500;
    margin-bottom: 0;
    vertical-align: middle;
}

.form h3 {
    font-weight: 300;
}

.nav.nav-pills li {
    border: 1px solid #5ba4d5;
    padding: 1px;
}

.nav.nav-pills li.no-border-left {
    border-left: none;
    padding: 1px 1px 1px 2px;            
}

.nav.nav-pills li.no-border-left a {
    border-right: 1px solid #fff;
}

.nav.nav-pills li.no-border-right {
    border-right: none;            
}

.nav.nav-pills li a {
    background-color: #fff;
    border-radius: 0;
    border-color: #fff;
    color: #bbb;
    
    font-size: 12px;
    padding: 15px;
}

.nav.nav-pills li.active a {
    background-color: #5ba4d5;
    color: #fff;
}

.nav.nav-pills li+li {
    margin: 0;
}

/**     
* END PILLS
*/

/**     
* TABLES
*/

table {
    font-size: 14px;
}

th {
    background: none;
    position: relative;
    color: #b0b0b0;
    font-size: 13px;
    
}

td {
    color: #474747;
}

td:last-child  {
    color: #474747;
    font-size: 12px;
}
 
td a {
    color: #2d2f34;
    font-size: 15px;
    font-weight: 400;
    vertical-align: middle;
    &:hover {
        color: #2d2f34;
    }
    &.view-details-btn {
        color: #5cb3ec;
        font-size: 14px;
        text-transform: lowercase;
        font-weight: 400;
        &:hover {
            color: lighten(#5cb3ec, 10%);
        } 
    }
    &.comment {
        display: inline-block; 
        max-width: 300px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        &:hover {
            color: #5cb3ec; 
        } 
    }
    &.configure-holder {
        background-color: transparent;
        border: 1px solid #5cb3ec;
        padding: 0 10px;
        display: inline-block; 
        border-radius: 6px;
        i {
            display: inline-block;
            margin-left: 5px;
            vertical-align: middle; 
        }
        span {
            color: #5cb3ec;
            font-size: 14px;
            line-height: 30px;
            font-weight: 400;
        }
        &:hover, &:active {
            background-color: #5cb3ec; 
            border-color: #5cb3ec; 
            i {
                filter: brightness(100);
            }
            span {
                color: #fff;
            }    
        } 
    }
}

.table thead>tr>th, .table tbody>tr>th, .table tfoot>tr>th, .table thead>tr>td, .table tbody>tr>td, .table tfoot>tr>td {
    padding: 15px 8px;
}

.table thead>tr>td, .table tbody>tr>td, .table tfoot>tr>td {
    height: 80px;
}

.table-lower thead>tr>td, .table-lower tbody>tr>td, .table-lower tfoot>tr>td {
    height: auto;
}

.table-lower thead>tr>th, .table-lower tbody>tr>th, .table-lower tfoot>tr>th, .table-lower thead>tr>td, .table-lower tbody>tr>td, .table-lower tfoot>tr>td {
    padding: 10px 8px;
}

table.table thead .sorting, table.table thead .sorting_asc, table.table thead .sorting_desc {
    background-image: none !important;
    cursor: pointer;
}

th.sorting {
    position: relative;
    white-space: nowrap;
    &.sorting_asc, &.sorting_desc {
        color: #5cb3ec !important;
    }
}

th.sorting:after {
    content: "\f0dc";
    display: inline-block;
    color: #D8D8D8;
    margin-right: 8px;
    font-family: 'FontAwesome';
    font-size: 13px;
    font-weight: 100;
    position: relative;
    top: 50%;
    margin-top: -8px;
}

th.sorting_asc:after {
    content: "\f0dd";
    color: #5cb3ec;
    margin-top: -11px;
    top: -3px;
    font-family: 'FontAwesome';
    font-size: 13px;
    font-weight: 100;
}

th.sorting_desc:after {
    content: "\f0de";
    color: #5cb3ec;
    margin-top: -5px;
    top: 3px;
    font-family: 'FontAwesome';
    font-size: 13px;
    font-weight: 100;
}
  

/**     
* END TABLES
*/



/**     
* PROGRESS BAR
*/


.progress {
    background-color: #DEDEDE;
    height: 5px;
    box-shadow: none;
}

.progress-bar {
    background-color: #24cf8c;
    box-shadow: none;
    border-radius: 3px;
}

.progress-striped .progress-bar {
    background-image: none; 
}

/**     
* PROGRESS BAR
*/

.breadcrumb li i.glyphicon-play {
    color: #26292f !important;
    font-size: 10px;
}

/**
* ALERT
*/

.alert {
    border-radius: 0 !important;
    text-align: center;

    .btn {
        margin: 0 15px;
    }
}

/**
* END ALERT
*/


/**
* Begin Modal
*/

.modal-header .close {
    margin-top: 8px;
    margin-right: 20px;

    &:active {
        outline: 0 none;
    }    
}

.modal-open,
.modal-open .navbar-fixed-top,
.modal-open .navbar-fixed-bottom {
    margin: 0;
}

/**
* POP-UP
*/

.modal-open {
    .datetimepicker {
        z-index: 10055;
    }

    .datepicker {
        z-index: 10055;
    }

    .page-sidebar {
        z-index: 1000;
    }

    .topbar {
        z-index: 999;
    }

    .tutorialize-slide {
        z-index: 1000;
    }

    .page-content {
        .page-breadcrumb {
            z-index: 998;
        }
    }
}

.modal-content {
    background-color: $main-modal-bg-color;
    border-radius: 0;
}

/**
* END POP-UP
*/

.note-editor {
    border: 0;
}

.note-editor .note-toolbar {
    background-color: #fff;
    border: 0;
    padding: 0 0 5px 0;
}

.note-editor .note-editable {
    border: 1px solid #e5e5e5;
}

.note-editor .note-statusbar .note-resizebar {
    border-color: #f5f5f5;
}


/**
* END SUMMERNOTE
*/


/**
* POPOVER
*/

.popover {
    background-color: rgba(64, 68, 77, .9);
    border-color: rgba(64, 68, 77, .9);
    color: #fff;
    white-space: normal;
    padding: 10px;
    box-shadow: 0 !important;
    border-radius: 6px;
}

.popover > .arrow {
   border-top-color: rgba(64, 68, 77, .7) !important;   
}

.popover.top .arrow {  
    border-bottom-color: rgba(64, 68, 77, .7) !important;
    &:after {
        border-top-color: rgba(64, 68, 77, .7) !important;     
    }
}


/**
* END POPOVER
*/



/**
* PAGE 404
*/

.page-404 .number {
    font-size: 200px;
    font-weight: 300;
    color: #84d5f7;
    display: block;
    text-align: center;
    margin-bottom: -60px;
}

.page-404 .details h2 {
    
    color: #90a1ba;
    font-size: 37px;
    margin: 0;
    text-align: center;
} 


/**
* END PAGE 404
*/

/**
* Glyphicons
*/
.glyphicon.blue {
    color: #93d4f6;
}
.glyphicon.red {
    color: #ef6868;
}
.glyphicon.gray {
    color: #67717f;
}
.glyphicon.big {
    font-size: 18px;
}
/**
* Glyphicons
*/

.well {
    background-color: #fff;
    border-radius: 0;
    border-color: #f7f7f7;
    box-shadow: none;
}

/**
* Theme Settings
*/

.settings-form .site-formats {
    background: none !important;
    padding-left: 0 !important;
    color: #2a313b;
    font-weight: 300;
}
.settings-form .panel {    
    border-color: #efefef;
}
.settings-form .panel .panel-heading {
    background-color: #f8f8f8;
    border-color: #efefef;
}
.settings-form .panel .panel-heading h3.color {
    background: image-url("img/colors-title-icon.png") no-repeat top left;
    color: #84c2e3;
    font-style: italic;
    
    font-weight: 500;
    font-size: 17px;
    padding-top: 7px; 
}
.settings-form .panel .panel-heading h3.variables {
    //background: image-url("img/variables-title-icon.png")no-repeat top left;
}
.settings-form .panel .panel-body .color-box {
    padding: 20px 0px;
}
.settings-form .panel .panel-body .color-box label {
    
    line-height: 51px;
    
}
.settings-form .panel .panel-body .skip-has-error {
    display: inline-block;
}
.settings-form .panel .panel-body .skip-has-error .colorpicker-element {
    margin-bottom: 0;
}
.settings-form .panel .panel-body .skip-has-error .colorpicker-element .input-group-addon {
    border-color: #fff;
}
.settings-form .panel .panel-body .skip-has-error .colorpicker-element .input-group-addon i {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    border: 1px solid #ccc;
}

.cke_inner {
    .cke_top {
        background-color: #f7f7f7 !important;
    }
    .cke_contents {
        border-right: none; 
        border-left: none; 
        border-bottom: none;
        border-top: 1px solid #dedede; 
    }
    .cke_bottom {
        border-right: none; 
        border-left: none; 
        border-bottom: none;    
    } 
}

/* #Start new
-------------------------------------------------------------------------------*/

.round-icon {
    display: inline-block;
    border: 1px solid;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    line-height: 18px;
    font-size: 12px;
    font-style: normal;
    text-align: center;
}

.round-icon-warning {
    border-color: #f96e6e;
    color: #f96e6e;
}

.round-icon-success {
    border-color: #5bff5b;
    color: #5bff5b;
}

.round-icon-location {
    border-color: #93c7ed;
    color: #93c7ed;
}

.action-link {
    font-size: 16px;
    display: inline-block;

    .round-icon {
        position: relative;
        top: -1px;
    }
}

.ico {
    display: inline-block;
}

.sweet-alert-plan-error {
    .lead {
        font-weight: normal;
    }

    .btn {
        margin: 0 7px;
    }

    .btn-success {
        &:focus {
            box-shadow: none;
        }
    }
}