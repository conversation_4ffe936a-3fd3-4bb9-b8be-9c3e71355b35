/* typography */
$analytics-black: #2D2F34;
$analytics-black-2: #000000de;
$analytics-black-3: #00000066;
$analytics-black-4: #00000099;
$analytics-black-5: #D7D7D7;
$analytics-forest-green: #129917;
$analytics-forest-red: #db2929;
$analytics-gallery: #eeeeee;
$analytics-gray-chateau: #a0a6b6;
$analytics-logan: #aeb1cd;
$analytics-medium-purple: #8d58e0;
$analytics-mischka: #cdcfe1;
$analytics-rose: #ff4b51;
$analytics-shamrock: #25cf8b;
$analytics-silver: #bdbdbd;
$analytics-white: #ffffff;
$analytics-grey: #B7B8C1;
$analytics-font-size-l: 16px;
$analytics-font-size-m: 14px;
$analytics-font-size-s: 13px;
$analytics-font-size-t: 11px;
$analytics-font-size-xl: 18px;
$analytics-font-size-xs: 12px;
$analytics-font-size-xxl: 20px;
$analytics-font-size-xxxl: 26px;

.roboto-normal-black-16px {
  color: $analytics-black-2;
  font-size: $analytics-font-size-l;
  font-style: normal;
  font-weight: 400;
}

.roboto-normal-black-14px {
  color: $analytics-black-3;
  font-size: $analytics-font-size-m;
  font-style: normal;
  font-weight: 400;
}

.roboto-normal-black-13px {
  color: $analytics-black-3;
  font-size: $analytics-font-size-s;
  font-style: normal;
  font-weight: 400;
}

.fontawesome5pro-regular-normal-forest-green-16px {
  color: $analytics-forest-green;
  font-size: $analytics-font-size-l;
  font-style: normal;
  font-weight: 400;
}

.fontawesome5pro-regular-normal-forest-red-16px {
  color: $analytics-forest-red;
  font-size: $analytics-font-size-l;
  font-style: normal;
  font-weight: 400;
}

.roboto-medium-black-20px {
  color: $analytics-black-4;
  font-size: $analytics-font-size-xxl;
  font-style: normal;
  font-weight: 500;
}

.roboto-normal-medium-purple-14px {
  color: $analytics-medium-purple;
  font-size: $analytics-font-size-m;
  font-style: normal;
  font-weight: 400;
}

.roboto-normal-black-12px {
  color: $analytics-black-4;
  font-size: $analytics-font-size-xs;
  font-style: normal;
  font-weight: 400;
}

.roboto-normal-black-14px-2 {
  color: $analytics-black-4;
  font-size: $analytics-font-size-m;
  font-style: normal;
  font-weight: 400;
}

.roboto-normal-medium-purple-16px {
  color: $analytics-medium-purple;
  font-size: $analytics-font-size-l;
  font-style: normal;
  font-weight: 400;
}

.roboto-medium-black-20px-2 {
  color: $analytics-black-2;
  font-size: $analytics-font-size-xxl;
  font-style: normal;
  font-weight: 500;
}

.fontawesome5pro-regular-normal-forest-green-20px {
  color: $analytics-forest-green;
  font-size: $analytics-font-size-xxl;
  font-style: normal;
  font-weight: 400;
}

.fontawesome5pro-regular-normal-forest-red-20px {
  color: $analytics-forest-red;
  font-size: $analytics-font-size-l;
  font-style: normal;
  font-weight: 400;
}

.roboto-medium-forest-green-20px {
  color: $analytics-forest-green;
  font-size: $analytics-font-size-xxl;
  font-style: normal;
  font-weight: 500;
}

.roboto-medium-forest-red-20px {
  color: $analytics-forest-red;
  font-size: $analytics-font-size-xxl;
  font-style: normal;
  font-weight: 500;
}

.roboto-normal-black-16px-2 {
  color: $analytics-black-4;
  font-size: $analytics-font-size-l;
  font-style: normal;
  font-weight: 400;
}

.roboto-normal-gray-chateau-14px {
  color: $analytics-gray-chateau;
  font-size: $analytics-font-size-m;
  font-style: normal;
  font-weight: 400;
}

.roboto-normal-gallery-12px {
  color: $analytics-gallery;
  font-size: $analytics-font-size-xs;
  font-style: normal;
  font-weight: 400;
}

.roboto-bold-mischka-13px {
  color: $analytics-mischka;
  font-size: $analytics-font-size-s;
  font-style: normal;
  font-weight: 700;
}

.normal-grey {
  color: $analytics-grey;
}

.fontawesome5pro-regular-normal-rose-16px {
  color: $analytics-rose;
  font-size: $analytics-font-size-l;
  font-style: normal;
  font-weight: 400;
}

.fontawesome5pro-light-white-18px {
  color: $analytics-white;
  font-size: $analytics-font-size-xl;
  font-style: normal;
  font-weight: 300;
}

.border-1px-black-5 {
  border: 1px solid $analytics-black-5;
}

.text-green,
.arrow-green {
    color: $analytics-forest-green;
}

.text-red,
.arrow-red {
    color: $analytics-forest-red;
}
/* end typography */

.flex-row {
  padding: 0 20px;
  max-width: 1450px;
  width: 100%;
  margin: auto;
}

.analytics-wrap {
    .container-fluid {
        padding: 0;
    }
}

.card-container {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  position: relative;
}

.cc-cards-grid {
  display: grid;
  gap: 26px;
  margin: 0 auto;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
}

/* cc-card */
.cc-card {
  align-items: center;
  border: 1px solid $analytics-black-5;
  background-color: $analytics-white;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  //margin-bottom: $analytics-font-size-xxxl;
  padding: 16px;
  position: relative;
  width: 100%;

  @media (min-width: 1200px) {
    min-height: 412px;
  }

  &.cc-card__details {
    display: block;
    min-height: auto;
    margin-bottom: $analytics-font-size-xxxl;

    .cc-subtitle__wrap {
        margin-top: 0;
    }

    .cc-subtitle__text {
        font-size: $analytics-font-size-l;
    }

    .no-data,
    .lds-ellipsis {
      position: relative;
      transform: none;
    }
  }

  a {
    text-decoration: none;
  }

  a:hover {
    opacity: .8;
  }

    .cc-card__inner {
      width: 100%;
    }

  .cc-card__title-wrap {
      align-items: flex-start;
      display: flex;
      justify-content: space-between;
      width: 100%;
      margin-bottom: 10px;
  }

  .cc-card__title {
    color: $analytics-black;
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 21px;
    display: inline-block;
    width: 70%;
  }

  select {
    &.cc-card__title {
      background-color: #fff;
      border: 0 !important;
      padding: 0;
      width: auto;
      height: auto;
    }
  }

  .cc-card__link {
    color: $analytics-medium-purple;
    font-size: $analytics-font-size-m;
    font-style: normal;
    font-weight: 400;
  }

  .sub-title {
    align-items: flex-start;
    display: flex;
    margin-left: auto;
    min-width: 50px;
    position: relative;
    justify-content: center;
  }

  .cc-card__arrow {
    align-items: flex-start;
    align-self: center;
    display: flex;
    margin-bottom: 2px;
    width: 12px;

      i {
        font-size: 16px;
      }
  }

  .cc-card__stats {
    align-items: flex-start;
    display: flex;
    flex-flow: wrap;
    width: 100%;

    .box-item__text-help {
        font-size: 14px;

        .single-chart {
            width: 20px;
        }
    }
  }

  .cc-card__stats-text-current {
    color: $analytics-black;
    font-weight: 500;
    font-size: $analytics-font-size-xxxl;
    line-height: 32px;
    display: flex;

    .box-item__text-help {
      margin-left: 10px;
    }
  }

  .cc-card__stats-text-prev {
    color: $analytics-grey;
    font-weight: 400;
    font-size: $analytics-font-size-s;
    line-height: 22px;

    &.tiny {
      font-size: $analytics-font-size-t;
    }
  }

    .cc-card__stats-text-prev-value {
      color: $analytics-black;
    }

  .cc-card__text-percent {
    font-weight: 500;
    font-size: 22px;
    line-height: 32px;
    white-space: nowrap;
  }

  /* end cc-card */
    .box-table {
      align-items: flex-start;
      border: 0;
      //display: flex;
      flex-direction: column;
      position: relative;
      width: 100%;
    }

  .box-item__wrap {
    width: 100%;
  }

  .box-item {
    align-items: center;
    //display: flex;
    margin-bottom: 12px;
    position: relative;
    width: 100%;
    justify-content: space-between;
    border: 0;
    border-bottom: 1px solid #e5e5e5;

    &:last-child {
        margin-bottom: 0;
      border-bottom: none;
    }

    td {
      border: 0;
      padding: 10px 0;
    }
  }

  .box-item__column-left {
    .box-item__text {
      padding-right: 30px;
    }
  }

  .box-item__column-right {
    width: 20%;
  }

  .box-item__column-center,
  .box-item__column-right {
    margin-left: auto;
  }


  .box-item__text {
    color: $analytics-black;
    font-weight: 400;
    font-size: 14px;
    line-height: 23px;
    white-space: nowrap;
    display: flex;
    align-items: center;

    &.end {
      justify-content: end;
    }

    a {
      font-size: 13px;
    }

    img {
      max-width: 35px;
      + a {
        margin-left: 10px;
      }
    }

    + .box-item__text-help {
      margin-top: 5px;
    }
  }

  .box-item__text-help {
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 13px;
    color: $analytics-grey;
  }

  .box-item__device {
      display: flex;
      align-items: center;

      +.box-item__device {
        margin-left: 10px;
      }

      .single-chart {
        margin-right: 4px;
      }
  }

  //.box-item {
  //  .box-item__text-help {
  //    display: block;
  //  }
  //  .box-item__device {
  //    + .box-item__device {
  //      margin-left: 0;
  //    }
  //  }
  //}

  .cc-card__stats-text + .box-item__text-help {
      align-self: center;
      margin-left: 20px;
  }

    .box-item__arrow-small {
      margin-bottom: 2px;
      width: 12px;

      i {
          font-size: 16px;
          font-weight: 400;
          line-height: 20px;
      }
    }

  .cc-subtitle__wrap {
    align-items: center;
    display: flex;
    margin-top: 25px;
    margin-bottom: 16px;
    position: relative;
    justify-content: space-between;
    width: 100%;

    + .box-table {
        margin-top: 0;
    }
  }

  .cc-subtitle__text {
    color: #2D2F34;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
  }

  .cc-subtitle__value {
    color: #2D2F34;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
  }

  /* percents & arrows */
  .box-item__percent-wrap {
    min-width: 42px;
    display: block;
    text-align: right;

    &.no-compare {
      display: block;
      text-align: right;
      .box-item__text {
        display: block;
      }
    }

    .box-item__percent,
    .box-item__arrow-small {
      display: inline-block;
    }
  }

  .long-arrow-alt-up-1 {
    height: 24px;
    letter-spacing: 0;
    line-height: 24px;
    margin-top: -1px;
    white-space: nowrap;
  }

  .cc-card__percent {
    white-space: nowrap;
  }

  .long-arrow-alt-down {
    color: #ff6167;
    font-family: "Font Awesome 5 Pro";
    font-size: $analytics-font-size-l;
    font-weight: 400;
    height: 24px;
    letter-spacing: 0;
    line-height: 24px;
    margin-top: -1px;
    white-space: nowrap;
  }
  /* end percents & arrows */

  .short-text {
    width: auto;
    white-space: normal;
  }

  .text-break-all {
    word-break: break-all;
    width: 100%;
  }

  @media (min-width: 1200px) {
    .no-data,
    .lds-ellipsis {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}

.mx-datepicker-main {
    .mx-table {
        border: 0;
    }

    .mx-table th {
        color: inherit;
        text-transform: none;
    }

    .mx-datepicker-sidebar {
        width: 200px;
    }

    .mx-datepicker-sidebar+.mx-datepicker-content {
        margin-left: 200px;
    }

    .mx-btn-shortcut {
        line-height: 29px;
        width: 100%;
    }

    .mx-btn-shortcut:hover {
        background-color: #eee6fa;
    }
}

.controls-wrap {
    display: flex;
    align-items: center;
    padding: 15px 0;

      .mx-datepicker-range {
        width: 235px;
        min-width: 235px;
        line-height: 0;
      }

      .mx-input {
        border-color: $analytics-black-5;
        font-weight: 400;
        font-size: 16px;
        line-height: 19px;
      }
}

.compare-box {
    border: 1px solid $analytics-black-5;
    color: $analytics-black;
    background-color: $analytics-white;
    line-height: 1.5;
    font-weight: 400;
    font-size: 16px;
    display: block;
    padding: 6px 10px;
    border-radius: 4px;
    margin-left: 10px;
    height: auto;
    width: auto;
}

.device-diagram__wrap {
    display: flex;
    align-items: center;
    align-self: flex-start;

    .device-diagram {
        color: $analytics-grey;
        font-size: $analytics-font-size-m;
        display: flex;
        align-items: center;

        + .device-diagram {
            margin-left: 50px;
        }

        .circle-bg {
          stroke: #EBEBF3;

        }

        .circle-bg,
        .circle {
            stroke-width: 4;
        }

        .single-chart {
            font-size: $analytics-font-size-l;
            width: 64px;
            margin-left: 15px;

            .percentage {
              fill: $analytics-black;
            }
        }
    }
}

.bold-black {
  color: $analytics-black;
  font-weight: bold;
}

@media (max-width: 1599px) and (min-width: 1200px) {
    .analytics-wrap .col-lg-4 {
        width: 50%;
    }
}

@media (max-width: 1400px) {
    .flex-row {
        padding: 0;
    }

    .cc-card .box-item__text {
        line-height: 20px;
        white-space: normal;
    }

//     .cc-card .box-item__column-left {
//         width: 100px;
//     }

    .cc-card .cc-card__stats-text,
    .cc-card .cc-card__text-percent {
        font-size: 20px;
        white-space: nowrap;
    }
}