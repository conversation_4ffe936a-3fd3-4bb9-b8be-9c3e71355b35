<?php

declare(strict_types=1);

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Core\Core\Models\Alerts;

class UpdateAlertsTableAddType extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        if (Schema::hasTable('alerts') && !Schema::hasColumn('alerts', 'type')) {
            Schema::table('alerts', function (Blueprint $table): void {
                $table->string('type')->default(Alerts::TYPE_ALERT)
                    ->index('idx_type')->after('mail_sent');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        //
    }
}
