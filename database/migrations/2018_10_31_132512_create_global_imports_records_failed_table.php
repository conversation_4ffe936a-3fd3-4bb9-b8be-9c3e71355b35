<?php

declare(strict_types=1);

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateGlobalImportsRecordsFailedTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        if (!Schema::hasTable('global_imports_records_failed')) {
            Schema::create('global_imports_records_failed', function (Blueprint $table): void {
                $table->increments('id');
                $table->unsignedInteger('record_id')->index()->nullable();
                $table->string('record_type')->index()->nullable();
                $table->nullableMorphs('relation');
                $table->string('type')->index()
                    ->comment('categories,vendors,suppliers,products,customers');
                $table->longText('data');
                $table->unsignedTinyInteger('try')->default(0)->index();
                $table->text('exception');
                $table->string('job_id')->index();
                $table->timestamps();

                $table->index('created_at', 'idx_created_at');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('global_imports_records_failed');
    }
}
