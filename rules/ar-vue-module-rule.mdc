---
description:
globs:
alwaysApply: false
---
# Vue Module Development Rule for CloudCart SiteCP

## Description
This rule provides comprehensive guidelines for creating and structuring Vue3 modules within CloudCart's sitecp (`vuejs-sitecp/src/CcModules/`). Vue modules are automatically discovered and initialized through their `setup.js` files, providing a modular architecture for admin panel features.

## 1. Module Discovery and Initialization

### 1.1 Automatic Module Scanning
- Vue scans the `vuejs-sitecp/src/CcModules/` directory for `setup.js` files
- Each module directory must contain a `setup.js` file to be recognized
- Modules are automatically registered and initialized during application startup

### 1.2 Module Naming Convention
- Use **PascalCase** for module directory names
- Examples: `CcAnalytics`, `Gdpr`, `ThemeSettings`, `PaymentProviders`
- Avoid special characters and spaces in module names

## 2. Required Module Structure

### 2.1 Essential Files and Directories
```
ModuleName/
├── setup.js                    # REQUIRED - Module entry point
├── router/
│   └── index.js               # Route definitions
├── components/
│   ├── Index.vue              # Main module component
│   ├── Tabs/                  # Tab components
│   ├── Modals/                # Modal components
│   ├── Helpers/               # Helper components
│   └── TableColumns/          # Table-specific components
├── js/                        # JavaScript utilities and composables
│   ├── composables/           # Vue 3 composables
│   ├── utils/                 # Utility functions
│   ├── api/                   # API Model classes
│   ├── constants/             # Module constants
│   └── stores/                # Pinia stores (if needed)
├── scss/                      # Module-specific styles
├── plugins/                   # Vue plugins (optional)
└── config.json               # Module configuration (optional)
```

### 2.2 setup.js - Module Entry Point
**REQUIRED**: Every module must have a `setup.js` file that exports module configuration.

**Basic structure:**
```javascript
import {routes} from './router';

export {
    routes,
}
```

**Advanced structure with plugins:**
```javascript
import {routes} from './router';
import {plugins} from './plugins';

export {
    routes,
    plugins,
}
```

## 3. Router Configuration

### 3.1 Router Structure
- Create `router/index.js` for route definitions
- Export `routes` array that will be merged into the main router
- Use dynamic imports for lazy loading components

### 3.2 Route Naming Convention
- Use dot notation for nested routes
- Start with module context (e.g., `apps.`, `admin.`, `core.`)
- Examples: `apps.gdpr`, `core.storefront.theme-settings`, `admin.analytics.overview`

### 3.3 Router Example
```javascript
let routes = [
  {
    name: "core.storefront.theme-settings",
    path: "/admin/new-storefront/theme-settings",
    component: () => import("./../components/Index"),
    props: {
      appKey: 'theme-settings',
    },
    children: [
      {
        name: "core.storefront.theme-settings.overview",
        path: "overview",
        component: () => import("./../components/Tabs/Overview"),
      },
      {
        name: "core.storefront.theme-settings.colors",
        path: "colors",
        component: () => import("./../components/Tabs/Colors"),
      }
    ]
  },
];

export { routes };
```

## 4. Component Organization

### 4.1 Main Component (Index.vue)
- Create `components/Index.vue` as the main module component
- Should handle module layout, navigation, and tab management
- Include module-specific logic and state management

### 4.2 Component Directory Structure
- **`Tabs/`** - Components for different module sections/tabs
- **`Modals/`** - Modal dialog components
- **`Helpers/`** - Reusable helper components
- **`TableColumns/`** - Table-specific components and column definitions

### 4.3 Component Naming
- Use **PascalCase** for component files
- Be descriptive: `ConsentSections.vue`, `AddressForm.vue`, `Requests.vue`
- Group related components in subdirectories

## 5. JavaScript Organization

### 5.1 JS Directory Structure
```
js/
├── composables/              # Vue 3 composables
├── utils/                    # Utility functions
├── api/                      # API Model classes (NEW)
├── constants/                # Module constants
└── stores/                   # Pinia stores (if needed)
```

### 5.2 Composables Pattern
- Create reusable logic as Vue 3 composables
- Name with `use` prefix: `useModuleSettings.js`, `useApiClient.js`
- Export composables that can be used across components

## 6. API Management (CloudCart Model Pattern)

### 6.1 Model-Based API Client
**ALWAYS use CloudCart's Model pattern** (`vuejs-sitecp/src/js/Eloquent/Model.js`) for API operations instead of custom axios clients.

**Benefits of Model pattern:**
- **Reactive data management** with Vue 3 reactivity
- **Automatic loading states** (`isFetching`, `isDeleting`)
- **Built-in error handling** with Sentry integration
- **CSRF protection** and proper headers
- **Consistent response handling** across all modules
- **Event system** for lifecycle hooks (creating, created, updating, updated, etc.)

### 6.2 Creating Model Classes
Create model classes in `js/api/` that extend the base Model:

```javascript
// js/api/ThemeSettings.js
import Model from '@js/Eloquent/Model';

class ThemeSettings extends Model {
    constructor() {
        super("/admin/api/new-storefront/theme-settings");
    }
}

export default ThemeSettings;
```

### 6.3 Using Models in Composables
```javascript
// js/composables/useThemeSettings.js
import { ref, computed } from 'vue';
import ThemeSettings from '../api/ThemeSettings';

export function useThemeSettings() {
    const model = new ThemeSettings();
    const settings = ref({});
    const loading = computed(() => model.isFetching);
    
    const fetchSettings = async () => {
        try {
            settings.value = await model.all();
        } catch (error) {
            // Error automatically handled by Model class
            console.error('Failed to fetch settings:', error);
        }
    };
    
    const updateSettings = async (data) => {
        try {
            const updated = await model.update(data);
            settings.value = updated;
            return updated;
        } catch (error) {
            throw error; // Let component handle validation errors
        }
    };
    
    return {
        settings,
        loading,
        fetchSettings,
        updateSettings,
        model // Expose model for advanced operations
    };
}
```

### 6.4 Available Model Methods
The Model class provides all standard CRUD operations:
- `find(id)` - Fetch single record
- `all()` - Fetch all records
- `paginate(page, perPage)` - Paginated results
- `create(data)` - Create new record
- `update(data, id)` - Update existing record
- `patch(data, id)` - Partial update
- `delete(id)` - Delete record
- `deleteBulk(ids)` - Bulk delete
- `post(id, data)` - Custom POST operations

### 6.5 Event Hooks
```javascript
const model = new ThemeSettings();

// Listen to lifecycle events
model.creating((data) => {
    console.log('About to create:', data);
    return true; // Return false to prevent operation
});

model.created((data) => {
    console.log('Successfully created:', data);
});

model.updating((data) => {
    console.log('About to update:', data);
});

model.updated((data) => {
    console.log('Successfully updated:', data);
});
```

### 6.6 Custom Model Methods
Extend models with custom methods for specific operations:

```javascript
class ThemeSettings extends Model {
    constructor() {
        super("/admin/api/new-storefront/theme-settings");
    }
    
    async validateField(field, value) {
        try {
            const response = await this.post('validate', { field, value });
            return response;
        } catch (error) {
            throw error;
        }
    }
    
    async resetToDefaults(group) {
        try {
            const response = await this.post(`reset/${group}`);
            return response;
        } catch (error) {
            throw error;
        }
    }
    
    async generatePreview(settings) {
        try {
            const response = await this.post('preview', settings);
            return response;
        } catch (error) {
            throw error;
        }
    }
}
```

**DO NOT create custom axios clients** - always use the Model pattern for consistency and to leverage CloudCart's established error handling, logging, and state management infrastructure.

## 7. Plugins System (Optional)

### 7.1 Plugin Structure
If your module needs to extend global functionality:

```
plugins/
├── index.js                  # Plugin registration
├── PluginName/
│   ├── index.js
│   └── PluginComponent.vue
```

### 7.2 Plugin Registration
```javascript
// plugins/index.js
import {PluginOne} from './PluginOne'
import {PluginTwo} from './PluginTwo'

export const plugins = [
    PluginOne,
    PluginTwo,
]
```

### 7.3 Plugin Export in setup.js
```javascript
import {routes} from './router';
import {plugins} from './plugins';

export {
    routes,
    plugins,
}
```

## 8. Translations System

### 8.1 Component-Level Translations
- **Translations are handled at the component level** using a `translations` key in the component's `data()` section
- **CloudCart's translation tool** reads this data and handles automatic translation
- **No separate translation files needed** - translations are embedded in components

### 8.2 Translation Pattern in Components
```javascript
// In Vue component
export default {
    data() {
        return {
            translations: {
                "Module Settings": this.$t("Module Settings"),
                "Save Changes": this.$t("Save Changes"),
                "Reset to Default": this.$t("Reset to Default"),
                "Loading...": this.$t("Loading..."),
                "Error occurred": this.$t("Error occurred"),
            },
            // ... other data
        };
    },
    // ... rest of component
}
```

### 8.3 Translation Best Practices
- **Define all translatable strings** in the component's `translations` data property
- **Use descriptive keys** that clearly indicate the text purpose
- **Group related translations** logically within the translations object
- **CloudCart's translation tool** automatically processes these translations

## 9. Configuration (Optional)

### 9.1 Module Configuration
Create `config.json` for module-specific configuration:

```json
{
  "export": {
    "limit": 150000
  },
  "api": {
    "baseUrl": "/admin/api/module-name",
    "timeout": 30000
  },
  "features": {
    "advancedMode": true,
    "debugMode": false
  }
}
```

## 10. Development Best Practices

### 10.1 Module Isolation
- Keep modules self-contained
- Avoid dependencies on other modules
- Use shared utilities from `src/components/` and `src/js/`

### 10.2 State Management
- Use Vue 3 Composition API
- Create composables for shared state
- Use Pinia stores for complex state management

### 10.3 Performance Considerations
- Use lazy loading for routes and components
- Implement proper caching strategies
- Optimize bundle size with tree shaking

## 11. Testing

### 11.1 Testing Structure
```
__tests__/
├── components/
├── composables/
└── utils/
```

### 11.2 Testing Guidelines
- Write unit tests for composables and utilities
- Test component behavior and user interactions
- Mock API calls and external dependencies

## 12. Module Examples

### 12.1 Simple Module (Routes Only)
```javascript
// setup.js
import {routes} from './router';

export {
    routes,
}
```

### 12.2 Complex Module (With Plugins)
```javascript
// setup.js
import {routes} from './router';
import {plugins} from './plugins';

export {
    routes,
    plugins,
}
```

## 13. Integration Guidelines

### 13.1 Using Existing Components
- Leverage form components from `src/components/Form/`
- Use shared utilities and mixins
- Follow established patterns from existing modules

### 13.2 API Integration
- **ALWAYS use the Model pattern** from `@js/Eloquent/Model`
- Follow namespace conventions for endpoints
- Implement proper error handling and validation

## 14. Common Patterns

### 14.1 Module Navigation
- Use tabbed interface for complex modules
- Implement breadcrumb navigation
- Provide clear entry points

### 14.2 Data Management
- Implement CRUD operations using Model pattern
- Use reactive data patterns
- Provide real-time updates where appropriate

### 14.3 User Experience
- Implement loading states via Model's reactive properties
- Provide clear feedback messages
- Follow CloudCart's UX patterns

## 15. Troubleshooting

### 15.1 Module Not Loading
- Ensure `setup.js` exists and exports properly
- Check for JavaScript errors in console
- Verify route paths and component imports

### 15.2 Route Conflicts
- Use unique route names
- Check for path conflicts with existing routes
- Verify proper route hierarchy

### 15.3 API Issues
- Verify Model endpoint configuration
- Check network tab for actual API calls
- Review Model error responses and Sentry logs

## 16. Migration and Updates

### 16.1 Updating Existing Modules
- Maintain backward compatibility
- Update dependencies gradually
- Test thoroughly after changes

### 16.2 Module Deprecation
- Follow deprecation guidelines
- Provide migration paths
- Document breaking changes

This rule ensures consistent, maintainable, and scalable Vue module development within CloudCart's sitecp architecture, leveraging the established Model pattern for reliable API management.
