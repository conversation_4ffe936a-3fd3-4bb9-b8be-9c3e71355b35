<!doctype html>
{%- liquid
  if settings.use_rtl == '1'
    assign isRTL = true
  elsif settings.use_rtl == '2' and settings.list_rtl contains request.locale.iso_code
    assign isRTL = true
  else
    assign isRTL = false
  endif
  assign body_img = settings.body_bg_image
  if body_img != blank and settings.general_layout == 'boxed'
    assign class_lazy = 'lazyloadbee'
  endif -%}
{%- capture class_html -%}
beep-theme bee-wrapper__{{settings.general_layout}} rtl_{{isRTL}} swatch_color_style_{{settings.swatch_color_style}} pr_img_effect_{{settings.pr_img_effect}} enable_eff_img1_{{settings.enable_eff_img1}} badge_shape_{{settings.badge_shape}} badge_reverse_color_{{ settings.badge_reverse_color }} css_for_wis_app_{{settings.enable_css_wis}} bee-lzcus-{{settings.use_cus_lz}} bee-pr_respon_mb_{{settings.pr_responsive_mb}} bee-header__{{settings.header_design}} is-remove-unavai-{{settings.variant_remove}} bee_compare_{{settings.enable_compe}}
{%- endcapture -%}
<html class="{{class_html}} no-js" lang="{{ request.locale.iso_code }}"{% if isRTL %} dir="rtl"{% endif %}>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="">
    <link rel="canonical" href="{{ canonical_url }}">
    <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>
    
    {%- if settings.favicon != blank %}<link rel="shortcut icon" type="image/png" href="{{ settings.favicon | image_url: width: 32, height: 32 }}"><link id="bee-favico" rel="apple-touch-icon-precomposed" type="image/png" sizes="152x152" href="{{ settings.favicon | image_url: width: 152, height: 152 }}">{% endif -%}

    {%- if settings.font_source == '1' and settings.fnt_fm_sp1.system? == false or settings.fnt_fm_sp2.system? == false or settings.fnt_fm_sp3.system? == false -%}
      <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    {%- endif -%}

    {%- capture seo_title -%}
      {%- if template == 'search' and search.performed == true -%}{{ 'search.general.heading' | t: count: search.results_count }}: {{ 'search.results_with_count_and_term' | t: terms: search.terms, count: search.results_count }}{%- elsif template == 'search.wishlist' %}{{ 'wishlist_page.meta' | t }}{%- elsif template == 'search.compare' %}{{ 'compare_page.meta' | t }}{%- else -%}{{ page_title }}{%- endif -%}
      {%- if current_tags -%}{%- assign meta_tags = current_tags | join: ', ' %} &ndash; {{ 'general.meta.tags' | t: tags: meta_tags -}}{%- endif -%}
      {%- if current_page != 1 %} &ndash; {{ 'general.meta.page' | t: page: current_page }}{%- endif -%}
      {%- assign escaped_page_title = page_title | escape -%}
      {%- unless escaped_page_title contains shop.name %} &ndash; {{ shop.name }}{%- endunless -%}
    {%- endcapture -%}
    <title>{{ seo_title | strip }}</title>
    <meta name="description" content="{{ page_description | default:shop.description | default: shop.name | escape }}">

    {%- liquid 
      assign t_name = request.page_type
      assign body_img = settings.body_bg_image
      render 'meta-tags',t_name:t_name -%}
    
    <script src="{{ 'lazysizes.min.js' | asset_url }}" async="async"></script>
    {{ content_for_header }}
    {%- render 'head_assets',t_name:t_name,isRTL:isRTL -%}
    
  </head>

  <body class="template-{{ request.page_type | handle }} {{class_lazy}}"{% if body_img != blank %} data-bgset="{{ body_img | image_url: width: 1 }}" data-optimumx="1.5" data-sizes="auto"{% endif -%}>
    <a class="skip-to-content-link button visually-hidden" href="#MainContent">{{ "accessibility.skip_to_text" | t }}</a>
    <div class="bee-close-overlay bee-op-0"></div>

    <div class="bee-website-wrapper">
      {%- section 'main-password-header' -%}
      <main id="MainContent" class="content-for-layout focus-none" role="main" tabindex="-1">
        {{ content_for_layout }}
      </main>
      {%- comment %}{%- section 'main-password-footer' -%}{% endcomment -%}
    </div>
  </body>
</html>
