{%- comment -%} Remove custom CSS includes if not needed {%- endcomment -%}
{%- comment -%} {{ 'component-article-card.css' | asset_url | stylesheet_tag }} {%- endcomment -%}
{%- comment -%} {{ 'component-card.css' | asset_url | stylesheet_tag }} {%- endcomment -%}
{%- comment -%} {{ 'section-main-blog.css' | asset_url | stylesheet_tag }} {%- endcomment -%}

{%- paginate blog.articles by 6 -%}
  <div
    id="main_blog_page"
    class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
    style="padding-top: {{ section.settings.padding_top }}px; padding-bottom: {{ section.settings.padding_bottom }}px;"
  >
    <h1 class="text-3xl font-bold mb-8 {{ settings.animations_reveal_on_scroll | default: '' }}">
      {{ blog.title | escape }}
    </h1>

    <div class="grid gap-8 {{ section.settings.layout == 'collage' ? 'md:grid-cols-2 lg:grid-cols-3' : 'md:grid-cols-3' }}">
      {%- for article in blog.articles -%}
        <div
          class="bg-white rounded-lg shadow p-6 flex flex-col {{ settings.animations_reveal_on_scroll | default: '' }}"
          {% if settings.animations_reveal_on_scroll %}
            data-cascade
            style="--animation-order: {{ forloop.index }};"
          {% endif %}
        >
          {%- render 'article-card',
            article: article,
            media_height: section.settings.image_height,
            media_aspect_ratio: article.image.aspect_ratio,
            show_image: section.settings.show_image,
            show_date: section.settings.show_date,
            show_author: section.settings.show_author,
            show_excerpt: true
          -%}
        </div>
      {%- endfor -%}
    </div>

    {%- if paginate.pages > 1 -%}
      {%- render 'pagination', paginate: paginate -%}
    {%- endif -%}
  </div>
{%- endpaginate -%}

{% schema %}
{
  "name": "t:sections.main-blog.name",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "t:sections.main-blog.settings.header.content"
    },
    {
      "type": "select",
      "id": "layout",
      "options": [
        {
          "value": "grid",
          "label": "t:sections.main-blog.settings.layout.options__1.label"
        },
        {
          "value": "collage",
          "label": "t:sections.main-blog.settings.layout.options__2.label"
        }
      ],
      "default": "collage",
      "label": "t:sections.main-blog.settings.layout.label",
      "info": "t:sections.main-blog.settings.layout.info"
    },
    {
      "type": "checkbox",
      "id": "show_image",
      "default": true,
      "label": "t:sections.main-blog.settings.show_image.label"
    },
    {
      "type": "select",
      "id": "image_height",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.main-blog.settings.image_height.options__1.label"
        },
        {
          "value": "small",
          "label": "t:sections.main-blog.settings.image_height.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.main-blog.settings.image_height.options__3.label"
        },
        {
          "value": "large",
          "label": "t:sections.main-blog.settings.image_height.options__4.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.main-blog.settings.image_height.label",
      "info": "t:sections.main-blog.settings.image_height.info"
    },
    {
      "type": "checkbox",
      "id": "show_date",
      "default": true,
      "label": "t:sections.main-blog.settings.show_date.label"
    },
    {
      "type": "checkbox",
      "id": "show_author",
      "default": false,
      "label": "t:sections.main-blog.settings.show_author.label"
    },
    {
      "type": "paragraph",
      "content": "t:sections.main-blog.settings.paragraph.content"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ]
}
{% endschema %}
