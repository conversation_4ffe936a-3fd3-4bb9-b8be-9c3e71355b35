{%- assign modal_size = modal_size | default: 'max-w-lg' -%}
{%- assign show_header = show_header | default: true -%}
{%- assign show_footer = show_footer | default: true -%}
{%- assign modal_header = modal_header | default: 'Modal Header' -%}
<div 
  x-data="{
    open: false,
    modalSize: '{{ modal_size }}',
    showHeader: {{ show_header }},
    showFooter: {{ show_footer }},
    openModal() { 
      this.open = true; 
      document.body.classList.add('overflow-hidden');
    },
    closeModal() { 
      this.open = false; 
      document.body.classList.remove('overflow-hidden');
    }
  }"
  x-init="
    open = false;
    window.openExampleModal = () => { 
      open = true; 
      document.body.classList.add('overflow-hidden');
    };
  "
  x-effect="if (!open) document.body.classList.remove('overflow-hidden')"
>
  <div 
    x-cloak
    class="fixed inset-0 z-50 flex items-center justify-center bg-black/40 transition hidden"
    x-transition:enter="transition ease-out duration-200"
    x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100"
    x-transition:leave="transition ease-in duration-150"
    x-transition:leave-start="opacity-100"
    x-transition:leave-end="opacity-0"
    :style="open ? 'display: flex;' : 'display: none;'"
  >
    <div
      x-show="open"
      :class="`bg-white rounded-lg shadow-xl w-full ${modalSize}`"
      @click.away="closeModal"
      x-transition:enter="transition ease-out duration-200"
      x-transition:enter-start="opacity-0 scale-95"
      x-transition:enter-end="opacity-100 scale-100"
      x-transition:leave="transition ease-in duration-150"
      x-transition:leave-start="opacity-100 scale-100"
      x-transition:leave-end="opacity-0 scale-95"
    >
      <template x-if="showHeader">
        <div class="flex items-center justify-between px-6 pt-2 pb-2 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">{{ modal_header }}</h3>
          <button @click="closeModal" class="text-gray-400 hover:text-gray-600 focus:outline-none ml-4">
            <svg class="h-6 w-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>
      </template>
      <div class="px-6 py-4">
        <div class="text-gray-700">{{ modal_content | raw }}</div>
        <div class="mt-4 flex gap-2">
          <label class="flex items-center gap-2">
            <input type="checkbox" x-model="showHeader" class="rounded border-gray-300" />
            Show Header
          </label>
          <label class="flex items-center gap-2">
            <input type="checkbox" x-model="showFooter" class="rounded border-gray-300" />
            Show Footer
          </label>
          <label class="flex items-center gap-2">
            <span>Size:</span>
            <select x-model="modalSize" class="rounded border-gray-300 px-2 py-1">
              <option value="max-w-sm">Small</option>
              <option value="max-w-md">Medium</option>
              <option value="max-w-lg">Large</option>
              <option value="max-w-xl">X-Large</option>
              <option value="max-w-2xl">2X-Large</option>
              <option value="max-w-3xl">3X-Large</option>
            </select>
          </label>
        </div>
      </div>
      <template x-if="showFooter">
        <div class="px-6 py-3 border-t border-gray-200 flex justify-end">
          <button @click="closeModal" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300">Close</button>
        </div>
      </template>
    </div>
  </div>
</div>
