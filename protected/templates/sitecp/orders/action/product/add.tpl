{capture append="js"}
    <script type="text/javascript">
        $(function () {
            var $productAddForm = $('#productAddForm');
            let $previewCloseButton = $('#previewClose');

            handleProductVariantSelectors($productAddForm);
            $productAddForm.on('cc.ajax.success', function () {
                {if $preview}
                $('#order_preview').trigger('cc.ajax.reload');
                $previewCloseButton.trigger('cc.preview.close');

                {literal}
                $('.side-panel').first().css({right: 0})
                if($('.side-panel').length > 1){
                    $('.side-panel').eq(1).css({right: 0})
                }
                {/literal}
                {/if}
                $('#order_summary').trigger('cc.ajax.reload');
                $('#order_customer').trigger('cc.ajax.reload');
                $('#order_shipping_address').trigger('cc.ajax.reload');
                $('#order_billing_address').trigger('cc.ajax.reload');
            });
        });
    </script>
{/capture}
{strip}
    <div class="form">
        <form id="productAddForm" method="post" class="ajaxForm" action="{route('admin.orders.products.add', [$order->id, $product->id])}">
            {if $preview}
            <div class="box-title fixed-top">
                <div class="side-panel-header">
                    <div class="left-controls">
                        <div class="close" data-dismiss="panel"></div>
                        <h3>{$product->name}</h3>
                    </div>
                    <div class="right-controls">
                        <button class="btn btn-default" data-dismiss="panel">{t}global.cancel{/t}</button>
                        <button class="btn btn-primary js-add-category submit">{t}global.add{/t}</button>
                    </div>
                </div>
            </div>
            {/if}
            <div class="product-variant-selector" data-variants="{$variants_json}" data-editvariant="true">
                <div class="row">
                    <div class="main-fields-holder clearfix">
                        {foreach $parameters as $parameter => $values}
                            <div class="{if $parameters|count == 3}col-xs-4{else}col-xs-6{/if}">
                                <div class="form-group padding-top-0">
                                    <label class="control-label" for="p{$values@index+1}">{t}order.label.product_parameter{/t} {$parameter}</label>
                                    <select name="p{$values@index+1}" class="form-control parameter-select select2me" id="p{$values@index+1}" {if $values@index > 0}disabled="disabled"{/if} data-placeholder="{t}order.select_product_parameter{/t}">
                                        <option value=""></option>
                                        {foreach $values as $value}
                                            <option value="{$value}">{$value}</option>
                                        {/foreach}
                                    </select>
                                </div>
                            </div>
                        {/foreach}
                    </div>
                </div>
                {if $product->fields->isNotEmpty()}
                    {$chunked = $product->fields->chunk(2)}
                    {foreach $chunked as $chunk}
                    <div class="row">
                        {foreach $chunk as $field}
                        <div class="col-sm-6">
                            {include file="orders/action/product/options/{$field->type}.tpl" field=$field}
                        </div>
                        {/foreach}
                    </div>
                    <hr>
                    {/foreach}
                {/if}
                <input type="hidden" id="product_variant_id" name="variant_id"/>
                <div class="variant-form-fields hide">
                    <table class="listing product-variant-table table margin-top-20">
                        <thead>
                        <tr>
                            <th>{t}variant.sku{/t}</th>
                            <th>{t}variant.barcode{/t}</th>
                            <th>{t}variant.price{/t}</th>
                            <th>{t}variant.quantity{/t}</th>
                            <th>{t}variant.weight{/t}</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr class="variant_information">
                            <td>
                                <span data-parameter="sku_formatted"></span>
                            </td>
                            <td>
                                <span data-parameter="barcode_formatted"></span>
                            </td>
                            <td>
                                <span data-parameter="price_discounted_formatted" data-parameter-fall="price_formatted"></span>
                                <br>
                                <del><span data-parameter="old_price_formatted"></span></del>
                            </td>
                            <td>
                                <span data-parameter="quantity_formatted"></span>
                            </td>
                            <td>
                                <span data-parameter="weight_formatted"></span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="main-box clearfix margin-top-20">
                        <div class="row">
                            <div class="col-xs-6">
                                <div class="form-group padding-0">
                                    <label class="control-label">{t}order.label.product_quantity{/t}</label>

                                    <input
                                            type="text"
                                            class="form-control js-quantity-select-control"
                                            name="quantity"
                                            value="{$product->variant->minimum_unit|default:1}"
                                    />
                                </div>
                            </div>
                            <div class="col-xs-6">
                                <div class="form-group padding-0">
                                    <label class="control-label">{t}order.label.product_price{/t}</label>
                                    <div class="input-group">
                                            <span class="input-group-addon input-group-addon-left">
                                                <input type="checkbox" class="checked" name="override_price" checked="checked"/>
                                                {t}order.label.product_price_override{/t}
                                            </span>
                                        <input type="text" name="price" class="form-control"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
{/strip}
