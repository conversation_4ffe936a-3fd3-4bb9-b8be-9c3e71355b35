<form class="ajaxForm" action="{route('admin.emails.update-password', ['id' => $id])}" method="post" role="form">
    <input type="hidden" name="password_validating" value="1" />
    <input type="hidden" name="password_change" value="1" />
    <div class="box">
        <div class="box text-left">
            <div class="box-title">
                <div class="box-title-text">
                    <h5>{t}domain.change_email_account_password{/t}</h5>
                </div>
            </div>

            <div class="box-section">

                <div class="row form-group padding-top-0">
                    <div class="col-xs-12">
                        <label class="control-label" for="current-password">{t}domain.label.current_password{/t}</label>
                        <input id="current-password" name="current_password" type="password" class="form-control" />
                    </div>
                </div>

                <div class="row form-group padding-top-0">
                    <div class="col-xs-12">
                        <label class="control-label" for="password">{t}domain.label.new_password{/t}</label>
                        <input id=password" name="password" type="password" class="form-control" />
                    </div>
                </div>

                <div class="row form-group padding-top-0">
                    <div class="col-xs-12">
                        <label class="control-label" for="password-confirmation">{t}domain.label.confirm_password{/t}</label>
                        <input id=password-confirmation" name="password_confirmation" type="password" class="form-control" />
                    </div>
                </div>

            </div>
        </div>
    </div>
</form>
{capture append="js"}
<script>
    $(function () {
        $(document).on('cc.ajax.success', '.ajaxForm', function () {
            window.location.reload();
        });
    });
</script>
{/capture}